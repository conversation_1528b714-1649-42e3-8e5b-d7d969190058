"""
Common utilities for live streaming data queries.
Provides shared functionality for table name generation, time conversion, and validation.
"""

import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union
from decimal import Decimal, ROUND_HALF_UP

from vups.logger import logger
from vups_server.sql.sentence.live_sql import get_danmu_table_name


class LiveQueryUtils:
    """Utility functions for live streaming data queries."""
    
    # Table name patterns
    MINUTE_TABLES = {
        'enter_room': 'enter_room_count_minute_table',
        'interact': 'interact_word_count_minute_table', 
        'active_watcher': 'active_watcher_count_minute_table',
        'online_rank': 'online_rank_count_minute_table',
        'danmu_count': 'danmu_count_minute_table',
        'income': 'income_minute_table'
    }
    
    PAYMENT_TABLES = ['gift_table', 'buy_guard_table', 'super_chat_table']
    
    @staticmethod
    def get_table_name(table_type: str, room_id: Optional[str] = None) -> str:
        """
        Get table name for a given type and optional room_id.
        
        Args:
            table_type: Type of table ('danmu', 'gift', 'superchat', etc.)
            room_id: Room ID for dynamic tables (like danmu)
            
        Returns:
            Full table name
            
        Raises:
            ValueError: If table_type is invalid or room_id missing for dynamic tables
        """
        if table_type == 'danmu':
            if not room_id:
                raise ValueError("room_id required for danmu table")
            return get_danmu_table_name(room_id)
        
        elif table_type == 'gift':
            return 'gift_table'
        
        elif table_type == 'superchat':
            return 'super_chat_table'
        
        elif table_type == 'buy_guard':
            return 'buy_guard_table'
        
        elif table_type == 'live_status':
            return 'live_status_minute_table'
        
        elif table_type == 'live_session':
            return 'live_session_table'
        
        elif table_type in LiveQueryUtils.MINUTE_TABLES:
            return LiveQueryUtils.MINUTE_TABLES[table_type]
        
        else:
            raise ValueError(f"Unknown table type: {table_type}")
    
    @staticmethod
    def validate_room_id(room_id: Any) -> str:
        """
        Validate and normalize room ID.
        
        Args:
            room_id: Room ID to validate
            
        Returns:
            Validated room ID as string
            
        Raises:
            ValueError: If room_id is invalid
        """
        if room_id is None:
            raise ValueError("room_id cannot be None")
        
        room_id_str = str(room_id).strip()
        
        if not room_id_str:
            raise ValueError("room_id cannot be empty")
        
        # Check if room_id contains only digits (basic validation)
        if not re.match(r'^\d+$', room_id_str):
            logger.warning(f"Room ID {room_id_str} contains non-digit characters")
        
        return room_id_str
    
    @staticmethod
    def validate_timestamp(timestamp: Any) -> int:
        """
        Validate and convert timestamp to integer.
        
        Args:
            timestamp: Timestamp to validate
            
        Returns:
            Validated timestamp as integer
            
        Raises:
            ValueError: If timestamp is invalid
        """
        if timestamp is None:
            raise ValueError("timestamp cannot be None")
        
        try:
            ts_int = int(timestamp)
            
            # Basic sanity check - timestamp should be reasonable
            # (between 2020 and 2050 in seconds, or milliseconds)
            if ts_int < 1577836800:  # 2020-01-01 in seconds
                if ts_int < 1577836800000:  # 2020-01-01 in milliseconds
                    raise ValueError(f"Timestamp {ts_int} appears to be too old")
            
            return ts_int
            
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid timestamp: {timestamp}") from e
    
    @staticmethod
    def validate_datetime(dt: Any) -> datetime:
        """
        Validate and convert datetime object.
        
        Args:
            dt: Datetime to validate
            
        Returns:
            Validated datetime object
            
        Raises:
            ValueError: If datetime is invalid
        """
        if dt is None:
            raise ValueError("datetime cannot be None")
        
        if isinstance(dt, datetime):
            return dt
        
        if isinstance(dt, str):
            try:
                # Try common datetime formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        return datetime.strptime(dt, fmt)
                    except ValueError:
                        continue
                
                raise ValueError(f"Unable to parse datetime string: {dt}")
                
            except Exception as e:
                raise ValueError(f"Invalid datetime string: {dt}") from e
        
        raise ValueError(f"Invalid datetime type: {type(dt)}")
    
    @staticmethod
    def validate_time_range(
        start_time: Any,
        end_time: Any,
        time_type: str = "auto"
    ) -> Tuple[Any, Any]:
        """
        Validate time range parameters.
        
        Args:
            start_time: Start time
            end_time: End time
            time_type: Type of time values ("timestamp", "datetime", "auto")
            
        Returns:
            Validated (start_time, end_time) tuple
            
        Raises:
            ValueError: If time range is invalid
        """
        if start_time is None or end_time is None:
            raise ValueError("start_time and end_time cannot be None")
        
        # Auto-detect time type
        if time_type == "auto":
            if isinstance(start_time, datetime) or isinstance(end_time, datetime):
                time_type = "datetime"
            else:
                time_type = "timestamp"
        
        # Validate based on type
        if time_type == "datetime":
            start_dt = LiveQueryUtils.validate_datetime(start_time)
            end_dt = LiveQueryUtils.validate_datetime(end_time)
            
            if start_dt >= end_dt:
                raise ValueError("start_time must be before end_time")
            
            return start_dt, end_dt
        
        elif time_type == "timestamp":
            start_ts = LiveQueryUtils.validate_timestamp(start_time)
            end_ts = LiveQueryUtils.validate_timestamp(end_time)
            
            if start_ts >= end_ts:
                raise ValueError("start_time must be before end_time")
            
            return start_ts, end_ts
        
        else:
            raise ValueError(f"Invalid time_type: {time_type}")
    
    @staticmethod
    def timestamp_to_datetime(timestamp: int) -> datetime:
        """
        Convert timestamp to datetime object.
        
        Args:
            timestamp: Unix timestamp (seconds or milliseconds)
            
        Returns:
            Datetime object
        """
        # Handle both seconds and milliseconds timestamps
        if timestamp > 1e10:  # Likely milliseconds
            timestamp = timestamp / 1000
        
        return datetime.fromtimestamp(timestamp)
    
    @staticmethod
    def datetime_to_timestamp(dt: datetime) -> int:
        """
        Convert datetime to timestamp.
        
        Args:
            dt: Datetime object
            
        Returns:
            Unix timestamp in seconds
        """
        return int(dt.timestamp())
    
    @staticmethod
    def calculate_income_from_gifts(gift_data: List[Dict]) -> Decimal:
        """
        Calculate income from gift data.
        
        Args:
            gift_data: List of gift records
            
        Returns:
            Total income as Decimal
        """
        total_income = Decimal("0.0")
        
        for gift in gift_data:
            if 'total_coin' in gift and gift['total_coin'] is not None:
                # Gift table: total_coin / 1000
                income = (Decimal(str(gift['total_coin'])) / Decimal(1000)).quantize(
                    Decimal("0.0"), rounding=ROUND_HALF_UP
                )
            elif 'gift_per_price' in gift and 'gift_num' in gift:
                # Buy guard table: gift_per_price * gift_num / 1000
                income = (
                    Decimal(str(gift['gift_per_price'])) * 
                    Decimal(str(gift['gift_num'])) / 
                    Decimal(1000)
                ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
            elif 'price' in gift and 'gift_num' in gift:
                # SuperChat table: price * gift_num
                income = Decimal(str(round(gift['price'] * gift['gift_num'], 1)))
            else:
                continue
            
            total_income += income
        
        return total_income
    
    @staticmethod
    def build_cache_key(prefix: str, *args, **kwargs) -> str:
        """
        Build a cache key from components.
        
        Args:
            prefix: Cache key prefix
            *args: Additional arguments
            **kwargs: Additional keyword arguments
            
        Returns:
            Cache key string
        """
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, datetime):
                key_parts.append(arg.isoformat())
            else:
                key_parts.append(str(arg))
        
        # Add keyword arguments (sorted for consistency)
        for key, value in sorted(kwargs.items()):
            if isinstance(value, datetime):
                key_parts.append(f"{key}={value.isoformat()}")
            else:
                key_parts.append(f"{key}={value}")
        
        return ":".join(key_parts)
    
    @staticmethod
    def get_time_field_for_table(table_name: str, operation: str = "query") -> str:
        """
        Get the appropriate time field name for a table.
        
        Args:
            table_name: Name of the table
            operation: Type of operation ("query", "insert")
            
        Returns:
            Time field name ("timestamp", "datetime", "start_timestamp", etc.)
        """
        if table_name == 'super_chat_table' and operation == "query":
            return 'start_timestamp'
        elif 'minute_table' in table_name:
            return 'timestamp'  # Most minute tables use timestamp for queries
        else:
            return 'datetime'   # Default for most tables
    
    @staticmethod
    def format_live_date(date_input: Union[str, datetime]) -> str:
        """
        Format date input to standard YYYY-MM-DD format.
        
        Args:
            date_input: Date as string or datetime object
            
        Returns:
            Formatted date string
            
        Raises:
            ValueError: If date format is invalid
        """
        if isinstance(date_input, datetime):
            return date_input.strftime('%Y-%m-%d')
        
        if isinstance(date_input, str):
            # Try to parse and reformat to ensure consistency
            try:
                dt = datetime.strptime(date_input, '%Y-%m-%d')
                return dt.strftime('%Y-%m-%d')
            except ValueError:
                try:
                    dt = datetime.strptime(date_input, '%Y-%m-%d %H:%M:%S')
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    raise ValueError(f"Invalid date format: {date_input}")
        
        raise ValueError(f"Invalid date type: {type(date_input)}")
    
    @staticmethod
    def safe_int(value: Any, default: int = 0) -> int:
        """Safely convert value to integer with default."""
        try:
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_float(value: Any, default: float = 0.0) -> float:
        """Safely convert value to float with default."""
        try:
            return float(value) if value is not None else default
        except (ValueError, TypeError):
            return default
