"""
Live status and session query module for live streaming data.
Provides optimized queries for live status and session management with time-based lookups.
"""

import copy
import time
from datetime import datetime, timedelta
from typing import List, Tuple, Optional, Dict

from vups.logger import logger
from vups_server.base.query_base import BaseQueryService
from .utils import LiveQueryUtils
from vups_server.query.live_query.live_query_templates import LiveQueryTemplates, LIVE_STATUS_COLUMNS, LIVE_SESSION_COLUMNS


class LiveStatusService(BaseQueryService):
    """
    Service class for live status and session related queries.

    Provides optimized database queries for:
    - Live status lookups
    - Current live information
    - Live session management
    - Live start/end time detection
    - Cross-day live session handling
    """

    def __init__(self, cache_ttl: int = 300):
        """
        Initialize LiveStatusQueries service.

        Args:
            cache_ttl: Cache TTL in seconds (default: 300 seconds for live status)
        """
        super().__init__(cache_ttl)
        self._query_templates = LiveQueryTemplates()

    async def query_live_status_by_room_and_time(
        self,
        room_id: str,
        ts: int,
        use_cache: bool = True
    ) -> int:
        """
        Query live status by room ID and timestamp.

        Args:
            room_id: Room ID
            ts: Timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Live status (0=offline, 1=live, 2=replay)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            ts_int = LiveQueryUtils.validate_timestamp(ts)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "live_status_time", room_id_str, ts_int
                )

            # Build optimized query
            query = """
                SELECT live_status
                FROM live_status_minute_table
                WHERE room_id = $1 AND timestamp = $2
                LIMIT 1
            """

            # Execute query
            result = await self._execute_enhanced_query(
                query=query,
                params=[room_id_str, ts_int],
                fetch_type="fetchval",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="live_status_time"
            )

            return LiveQueryUtils.safe_int(result, 0)

        except ValueError as e:
            logger.error(f"Validation error in live status query: {e}")
            return 0
        except Exception as e:
            logger.error(f"Error querying live status by time: {e}")
            return 0

    async def query_now_live_info_by_room(
        self,
        room_id: str,
        use_cache: bool = True
    ) -> Optional[Dict]:
        """
        Query current live information by room ID (optimized version).

        Args:
            room_id: Room ID
            use_cache: Whether to use caching

        Returns:
            Live information record or None
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "current_live_info", room_id_str
                )

            # Build optimized query (eliminates subquery)
            query = self._query_templates.build_latest_record_query(
                table_name="live_status_minute_table",
                columns=LIVE_STATUS_COLUMNS,
                filter_conditions="room_id = $1",
                order_field="timestamp"
            )

            # Execute query
            result = await self._execute_enhanced_query(
                query=query,
                params=[room_id_str],
                fetch_type="fetchrow",
                cache_key=cache_key,
                cache_ttl=60,  # Shorter cache for current info
                performance_key="current_live_info"
            )

            return dict(result) if result else None

        except ValueError as e:
            logger.error(f"Validation error in current live info query: {e}")
            return None
        except Exception as e:
            logger.error(f"Error querying current live info: {e}")
            return None

    async def query_live_start_end_time_by_live_date(
        self,
        room_id: str,
        live_date_str: str,
        date_is_start_live_date: bool = False,
        use_cache: bool = True
    ) -> Tuple[List[Dict], float]:
        """
        Query live start and end times by live date.

        Args:
            room_id: Room ID
            live_date_str: Live date in YYYY-MM-DD format
            date_is_start_live_date: Whether the date is the start live date
            use_cache: Whether to use caching

        Returns:
            Tuple of (time_pair_list, execution_time_ms)
        """
        start_time = time.time()
        result_time_pair_list = []
        temp_live_time_pair = {}

        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            live_date_format = LiveQueryUtils.validate_datetime(live_date_str)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "live_start_end_times", room_id_str, live_date_str, date_is_start_live_date
                )

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Query live status changes for the date
            query = """
                SELECT timestamp, live_status, live_action
                FROM live_status_minute_table
                WHERE room_id = $1
                  AND datetime >= $2::DATE
                  AND datetime < $2::DATE + INTERVAL '1 DAY'
                  AND (live_action = '开始直播' OR live_action = '结束直播')
                ORDER BY datetime
            """

            rows = await self._execute_query(
                query, [room_id_str, live_date_format, live_date_format], "fetch"
            )

            if not rows:
                elapsed_ms = (time.time() - start_time) * 1000
                final_result = ([], elapsed_ms)
                if cache_key:
                    await self.cache.set(cache_key, final_result)
                return final_result

            # Process live status changes
            for row in rows:
                if row["live_status"] == 1 and row["live_action"] == "开始直播":
                    start_time_str = datetime.fromtimestamp(row["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
                    temp_live_time_pair["start_time_str"] = start_time_str

                elif row["live_status"] in [0, 2] and row["live_action"] == "结束直播":
                    end_time_str = datetime.fromtimestamp(row["timestamp"]).strftime("%Y-%m-%d %H:%M:%S")
                    temp_live_time_pair["end_time_str"] = end_time_str

                    # Handle cross-day sessions
                    if not date_is_start_live_date and "start_time_str" not in temp_live_time_pair:
                        await self._handle_cross_day_start(
                            room_id_str, live_date_format, temp_live_time_pair
                        )

                    if "start_time_str" in temp_live_time_pair:
                        result_time_pair_list.append(copy.deepcopy(temp_live_time_pair))
                    temp_live_time_pair = {}

            # Handle ongoing sessions that cross to next day
            if date_is_start_live_date and rows and rows[-1]["live_status"] == 1:
                await self._handle_cross_day_end(
                    room_id_str, live_date_format, rows[-1], result_time_pair_list
                )

            elapsed_ms = (time.time() - start_time) * 1000
            final_result = (result_time_pair_list, elapsed_ms)

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, final_result)

            return final_result

        except ValueError as e:
            logger.error(f"Validation error in live start/end time query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying live start/end times: {e}")
            return [], 0

    async def _handle_cross_day_start(
        self,
        room_id_str: str,
        live_date_format: datetime,
        temp_live_time_pair: Dict
    ) -> None:
        """Handle cross-day session start time lookup."""
        try:
            prev_live_date = live_date_format - timedelta(days=1)
            prev_query = """
                SELECT timestamp, live_status, live_action
                FROM live_status_minute_table
                WHERE room_id = $1
                  AND datetime >= $2::DATE
                  AND datetime < $2::DATE + INTERVAL '1 DAY'
                  AND (live_action = '开始直播' OR live_action = '结束直播')
                ORDER BY datetime
            """

            prev_rows = await self._execute_query(
                prev_query, [room_id_str, prev_live_date, prev_live_date], "fetch"
            )

            if prev_rows:
                last_prev_row = prev_rows[-1]
                if last_prev_row["live_status"] == 1 and last_prev_row["live_action"] == "开始直播":
                    prev_start_ts = last_prev_row["timestamp"]
                    temp_live_time_pair["start_time_str"] = datetime.fromtimestamp(
                        prev_start_ts
                    ).strftime("%Y-%m-%d %H:%M:%S")

        except Exception as e:
            logger.error(f"Error handling cross-day start: {e}")

    async def _handle_cross_day_end(
        self,
        room_id_str: str,
        live_date_format: datetime,
        last_row: Dict,
        result_time_pair_list: List[Dict]
    ) -> None:
        """Handle cross-day session end time lookup."""
        try:
            last_start_time_str = datetime.fromtimestamp(
                last_row["timestamp"]
            ).strftime("%Y-%m-%d %H:%M:%S")

            next_live_date = live_date_format + timedelta(days=1)
            next_query = """
                SELECT timestamp, live_status, live_action
                FROM live_status_minute_table
                WHERE room_id = $1
                  AND datetime >= $2::DATE
                  AND datetime < $2::DATE + INTERVAL '1 DAY'
                  AND (live_action = '开始直播' OR live_action = '结束直播')
                ORDER BY datetime
            """

            next_rows = await self._execute_query(
                next_query, [room_id_str, next_live_date, next_live_date], "fetch"
            )

            if next_rows:
                first_next_row = next_rows[0]
                if first_next_row["live_status"] in [0, 2] and first_next_row["live_action"] == "结束直播":
                    next_end_time_str = datetime.fromtimestamp(
                        first_next_row["timestamp"]
                    ).strftime("%Y-%m-%d %H:%M:%S")
                    result_time_pair_list.append({
                        "start_time_str": last_start_time_str,
                        "end_time_str": next_end_time_str,
                    })

        except Exception as e:
            logger.error(f"Error handling cross-day end: {e}")

    async def query_live_start_time_by_end_time(
        self,
        room_id: str,
        end_time_str: str,
        use_cache: bool = True
    ) -> Tuple[str, float]:
        """
        Query live start time by end time.

        Args:
            room_id: Room ID
            end_time_str: End time string in YYYY-MM-DD HH:MM:SS format
            use_cache: Whether to use caching

        Returns:
            Tuple of (start_time_str, execution_time_ms)
        """
        execution_start_time = time.time()

        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            end_time_format = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "live_start_by_end", room_id_str, end_time_str
                )

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Query for start time within 24 hours before end time
            query = """
                SELECT timestamp
                FROM live_status_minute_table
                WHERE room_id = $1
                  AND datetime >= $2 - INTERVAL '24 HOURS'
                  AND datetime < $2
                  AND live_action = '开始直播'
                ORDER BY datetime DESC
                LIMIT 1
            """

            result_row = await self._execute_query(
                query, [room_id_str, end_time_format, end_time_format], "fetchrow"
            )

            if result_row:
                start_time_str = datetime.fromtimestamp(
                    result_row["timestamp"]
                ).strftime("%Y-%m-%d %H:%M:%S")
            else:
                # Fallback to start of day
                start_time_str = end_time_format.replace(
                    hour=0, minute=0, second=0, microsecond=0
                ).strftime("%Y-%m-%d %H:%M:%S")

            elapsed_ms = (time.time() - execution_start_time) * 1000
            final_result = (start_time_str, elapsed_ms)

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, final_result)

            return final_result

        except ValueError as e:
            logger.error(f"Validation error in live start time query: {e}")
            return "", 0
        except Exception as e:
            logger.error(f"Error querying live start time by end time: {e}")
            return "", 0

    def get_performance_summary(self) -> dict:
        """
        Get performance summary for live status queries.

        Returns:
            Dictionary with performance statistics
        """
        return {
            "live_status_time": self.get_performance_stats("live_status_time"),
            "current_live_info": self.get_performance_stats("current_live_info")
        }


live_status_service = LiveStatusService()
