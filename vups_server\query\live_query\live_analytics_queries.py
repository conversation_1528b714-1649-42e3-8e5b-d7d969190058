"""
Analytics query module for live streaming data.
Provides complex analytics queries that combine multiple data sources with efficient batch processing.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any

from vups.logger import logger
from vups_server.base.query_base import BaseQueryService
from .utils import LiveQueryUtils
from vups_server.query.live_query.live_query_templates import LIVE_SESSION_COLUMNS


class LiveAnalyticsService(BaseQueryService):
    """
    Service class for complex analytics queries.

    Provides optimized database queries for:
    - Complete live session information
    - Minute-level live data analytics
    - Room-based aggregated statistics
    - Multi-table data combination
    - Efficient batch processing
    """

    def __init__(self, cache_ttl: int = 600):
        """
        Initialize LiveAnalyticsService service.

        Args:
            cache_ttl: Cache TTL in seconds (default: 600 seconds for analytics)
        """
        super().__init__(cache_ttl)

    async def query_whole_live_info_with_live_id(
        self,
        live_id: str,
        use_cache: bool = True
    ) -> Optional[Dict]:
        """
        Query complete live information by live_id.

        Args:
            live_id: Live session ID
            use_cache: Whether to use caching

        Returns:
            Complete live information dictionary or None
        """
        live_info = {
            "liveId": live_id, "isFinish": False, "isFull": False, "parentArea": "", "area": "",
            "coverUrl": "", "danmakusCount": 0, "startDate": "", "stopDate": "", "title": "",
            "totalIncome": 0.0, "watchCount": 0, "likeCount": 0, "payCount": 0,
            "interactionCount": 0, "onlineRank": 0, "aveOnlineRank": 0, "aveEnterRoom": 0,
        }

        try:
            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key("whole_live_info", live_id)

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Query live status data
            status_data = await self._execute_query(
                """
                SELECT live_action, parent_area, area, cover, title
                FROM live_status_minute_table
                WHERE live_id = $1 ORDER BY timestamp DESC LIMIT 1
                """,
                [live_id],
                "fetchrow"
            )

            if status_data:
                # Get all live actions for this session
                actions_rows = await self._execute_query(
                    "SELECT DISTINCT live_action FROM live_status_minute_table WHERE live_id = $1",
                    [live_id],
                    "fetch"
                )
                actions = {row["live_action"] for row in actions_rows}
                live_info.update({
                    "isFinish": "结束直播" in actions,
                    "isFull": "开始直播" in actions
                })

            # Query session data
            session_data = await self._execute_query(
                f"""
                SELECT {', '.join(LIVE_SESSION_COLUMNS)}
                FROM live_session_table WHERE live_id = $1
                """,
                [live_id],
                "fetchrow"
            )

            if session_data:
                live_info.update({
                    "danmakusCount": session_data["danmu_count"] or 0,
                    "startDate": session_data["start_time_str"] or "",
                    "stopDate": session_data["end_time_str"] or "",
                    "parentArea": session_data["parent_area"] or "",
                    "area": session_data["area"] or "",
                    "coverUrl": session_data["cover"] or "",
                    "title": session_data["title"] or "",
                    "totalIncome": float(session_data["income"] or 0),
                    "watchCount": session_data["watch_change_count"] or 0,
                    "likeCount": session_data["like_info_update_count"] or 0,
                    "payCount": session_data["pay_count"] or 0,
                    "interactionCount": session_data["interaction_count"] or 0,
                    "onlineRank": session_data["max_online_rank"] or 0,
                    "enterRoom": session_data["enter_room_count"] or 0,
                    "aveOnlineRank": session_data["ave_online_rank"] or 0,
                    "aveEnterRoom": session_data["ave_enter_room"] or 0,
                })

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, live_info)

            return live_info

        except Exception as e:
            logger.error(f"Error querying whole live info for live_id {live_id}: {e}")
            return None

    async def query_minutes_live_info_with_live_id(
        self,
        live_id: str,
        interval: int = 10,
        use_cache: bool = True
    ) -> Optional[Dict]:
        """
        Query minute-level live information with specified interval.

        Args:
            live_id: Live session ID
            interval: Minute interval for data points
            use_cache: Whether to use caching

        Returns:
            Minute-level live information dictionary or None
        """
        try:
            # Get base live info first
            base_live_info = await self.query_whole_live_info_with_live_id(live_id, use_cache)
            if not base_live_info:
                logger.error(f"Unable to get base live info for live_id {live_id}")
                return None

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key("minutes_live_info", live_id, interval)

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            result = base_live_info.copy()
            result.update({
                "danmuCountList": [], "activeWatcherList": [], "onlineRankCountList": [],
                "incomeCountList": [], "interactCountList": [], "enterCountList": [], "timestamp": []
            })

            if not result.get("startDate"):
                logger.error(f"No start date for live_id {live_id}")
                return result

            # Calculate time points
            start_time = datetime.strptime(result["startDate"], "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(result["stopDate"], "%Y-%m-%d %H:%M:%S") if result.get("stopDate") else datetime.now()

            time_points = [
                start_time + timedelta(minutes=i * interval)
                for i in range(int((end_time - start_time).total_seconds() / 60 / interval) + 1)
            ]

            if not time_points:
                return result

            timestamps = [int(tp.timestamp()) for tp in time_points]
            result["timestamp"] = [tp.strftime("%Y-%m-%d %H:%M:%S") for tp in time_points]

            # Define minute tables and their aggregation functions
            minute_tables = {
                "danmuCountList": ("danmu_count_minute_table", "SUM", "count"),
                "activeWatcherList": ("active_watcher_count_minute_table", "MAX", "count"),
                "onlineRankCountList": ("online_rank_count_minute_table", "MAX", "count"),
                "incomeCountList": ("income_minute_table", "SUM", "income"),
                "interactCountList": ("interact_word_count_minute_table", "SUM", "count"),
                "enterCountList": ("enter_room_count_minute_table", "SUM", "count"),
            }

            # Query data for each time interval
            for i in range(len(time_points) - 1):
                start_ts, end_ts = timestamps[i], timestamps[i + 1]

                for key, (table, agg_func, field) in minute_tables.items():
                    query = f"""
                        SELECT COALESCE({agg_func}({field}), 0) as value
                        FROM {table}
                        WHERE live_id = $1 AND timestamp >= $2 AND timestamp < $3
                    """

                    value = await self._execute_query(query, [live_id, start_ts, end_ts], "fetchval")

                    if key == "incomeCountList":
                        value = float(Decimal(str(value or 0)).quantize(Decimal("0.0")))

                    result[key].append(value or 0)

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, result)

            return result

        except Exception as e:
            logger.error(f"Error querying minute live info for live_id {live_id}: {e}")
            return None

    async def query_live_info_with_room_id(
        self,
        room_id: str,
        use_cache: bool = True
    ) -> Dict:
        """
        Query comprehensive live information for a room.

        Args:
            room_id: Room ID
            use_cache: Whether to use caching

        Returns:
            Comprehensive room live information dictionary
        """
        result = {
            "lastLiveDate": "", "lastLiveDanmakuCount": 0, "lastLiveIncome": 0.0,
            "lastAveEnterRoomCount": 0, "lastEnterRoomCount": 0, "lastAveOnlineRankCount": 0,
            "totalDanmakuCount": 0, "totalIncome": 0.0, "totalLiveCount": 0,
            "totalLiveSecond": 0.0, "sessions": [],
        }

        try:
            # Validate input
            room_id_str = LiveQueryUtils.validate_room_id(room_id)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key("room_live_info", room_id_str)

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Query all sessions for statistics
            all_sessions = await self._execute_query(
                """
                SELECT live_id, danmu_count, start_time_str, end_time_str, income,
                       watch_change_count, ave_online_rank, ave_enter_room,
                       enter_room_count, datetime
                FROM live_session_table
                WHERE room_id = $1
                ORDER BY datetime DESC, start_time_str DESC
                """,
                [room_id_str],
                "fetch"
            )

            if not all_sessions:
                logger.info(f"No live sessions found for room {room_id_str}")
                return result

            # Calculate aggregated statistics
            result["totalLiveCount"] = len(all_sessions)
            result["totalDanmakuCount"] = sum(s["danmu_count"] for s in all_sessions if s["danmu_count"])
            result["totalIncome"] = float(sum(Decimal(str(s["income"])) for s in all_sessions if s["income"]))

            # Latest session info
            latest_session = all_sessions[0]
            result.update({
                "lastLiveDate": latest_session["datetime"].strftime("%Y-%m-%d") if latest_session["datetime"] else "",
                "lastLiveDanmakuCount": latest_session["danmu_count"] or 0,
                "lastLiveIncome": latest_session["income"] or 0,
                "lastWatchCount": latest_session["watch_change_count"] or 0,
                "lastAveOnlineRankCount": latest_session["ave_online_rank"] or 0,
                "lastAveEnterRoomCount": latest_session["ave_enter_room"] or 0,
                "lastEnterRoomCount": latest_session["enter_room_count"] or 0,
            })

            # Calculate total live duration
            total_duration = timedelta()
            for session in all_sessions:
                if session["start_time_str"] and session["end_time_str"]:
                    try:
                        start_dt = datetime.strptime(session["start_time_str"], "%Y-%m-%d %H:%M:%S")
                        end_dt = datetime.strptime(session["end_time_str"], "%Y-%m-%d %H:%M:%S")
                        if end_dt > start_dt:
                            total_duration += end_dt - start_dt
                    except (ValueError, TypeError) as e:
                        logger.warning(f"Error parsing time for live_id={session['live_id']}: {e}")

            result["totalLiveSecond"] = total_duration.total_seconds()

            # Get detailed session information
            live_ids = [s["live_id"] for s in all_sessions]
            if live_ids:
                tasks = [self.query_whole_live_info_with_live_id(live_id, use_cache=False) for live_id in live_ids]
                live_info_results = await asyncio.gather(*tasks, return_exceptions=True)
                result["sessions"] = [info for info in live_info_results if info and not isinstance(info, Exception)]

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, result)

            return result

        except ValueError as e:
            logger.error(f"Validation error in room live info query: {e}")
            return result
        except Exception as e:
            logger.error(f"Error querying live info for room {room_id}: {e}")
            return result

    def get_performance_summary(self) -> dict:
        """
        Get performance summary for analytics queries.

        Returns:
            Dictionary with performance statistics
        """
        return {
            "whole_live_info": self.get_performance_stats("whole_live_info"),
            "minutes_live_info": self.get_performance_stats("minutes_live_info"),
            "room_live_info": self.get_performance_stats("room_live_info")
        }


live_analytics_service = LiveAnalyticsService()
