"""
Specialized queries module.
Handles tieba queries, word cloud generation, followers management, and other specialized functions.
"""

import os
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

from vups.logger import logger
from vups_server.base.query_base import BaseQueryService


class UserSpecializedQueryService(BaseQueryService):
    """Service for specialized queries like tieba, word clouds, and followers."""

    def __init__(self):
        super().__init__(cache_ttl=900)  # 15 minutes cache for specialized queries

    async def get_tieba_data(self, uid: str) -> Dict:
        """
        Query comprehensive tieba data for a user.

        Args:
            uid: User UID

        Returns:
            Dictionary containing tieba statistics and data
        """
        cache_key = f"tieba_data_{uid}"

        # Get tieba statistics
        stats_query = """
            SELECT uid, name, tieba_total_num, tieba_total_reply_num,
                   tieba_total_agree_num, tieba_total_disagree_num, datetime
            FROM tieba_table
            WHERE uid = $1
            ORDER BY datetime DESC
            LIMIT 1
        """

        stats_result = await self._cached_query(
            cache_key=f"{cache_key}_stats",
            query=stats_query,
            params=[uid],
            fetch_type="fetchrow"
        )

        # Get tieba threads
        threads_query = """
            SELECT thread_id, thread_title, thread_content, thread_reply_num,
                   thread_agree_num, thread_disagree_num, thread_time,
                   thread_url, forum_name
            FROM tieba_threads_table
            WHERE uid = $1
            ORDER BY thread_time DESC
            LIMIT 50
        """

        threads_results = await self._cached_query(
            cache_key=f"{cache_key}_threads",
            query=threads_query,
            params=[uid],
            fetch_type="fetch"
        )

        tieba_data = {
            "stats": {},
            "threads": []
        }

        if stats_result:
            tieba_data["stats"] = {
                "uid": stats_result["uid"],
                "name": stats_result["name"],
                "total_num": stats_result["tieba_total_num"],
                "total_reply_num": stats_result["tieba_total_reply_num"],
                "total_agree_num": stats_result["tieba_total_agree_num"],
                "total_disagree_num": stats_result["tieba_total_disagree_num"],
                "datetime": (
                    stats_result["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                    if stats_result["datetime"]
                    else None
                ),
            }

        if threads_results:
            for thread in threads_results:
                tieba_data["threads"].append({
                    "thread_id": thread["thread_id"],
                    "title": thread["thread_title"],
                    "content": thread["thread_content"],
                    "reply_num": thread["thread_reply_num"],
                    "agree_num": thread["thread_agree_num"],
                    "disagree_num": thread["thread_disagree_num"],
                    "time": (
                        thread["thread_time"].strftime("%Y-%m-%d %H:%M:%S")
                        if thread["thread_time"]
                        else None
                    ),
                    "url": thread["thread_url"],
                    "forum_name": thread["forum_name"],
                })

        return tieba_data

    async def get_followers_list(
        self,
        uid: str,
        target_datetime: Optional[datetime] = None
    ) -> List[Dict]:
        """
        Query followers list for a user.

        Args:
            uid: User UID
            target_datetime: Specific datetime to query (defaults to latest)

        Returns:
            List of followers data
        """
        if target_datetime:
            cache_key = f"followers_list_{uid}_{target_datetime.isoformat()}"
            query = """
                SELECT uid, name, followers_list, datetime
                FROM followers_list_table
                WHERE uid = $1 AND datetime <= $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            params = [uid, target_datetime]
        else:
            cache_key = f"followers_list_latest_{uid}"
            query = """
                SELECT uid, name, followers_list, datetime
                FROM followers_list_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            params = [uid]

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=params,
            fetch_type="fetchrow"
        )

        if result and result["followers_list"]:
            try:
                import json
                followers_data = json.loads(result["followers_list"])
                return followers_data if isinstance(followers_data, list) else []
            except (json.JSONDecodeError, TypeError):
                logger.error(f"Failed to parse followers list for UID={uid}")
                return []

        return []

    async def get_followers_review_data(self, uid: str) -> Dict:
        """
        Query followers review data and calculate review rate.

        Args:
            uid: User UID

        Returns:
            Dictionary containing review data and rate
        """
        cache_key = f"followers_review_{uid}"

        query = """
            SELECT uid, name, followers_review_list, datetime
            FROM followers_review_list_table
            WHERE uid = $1
            ORDER BY datetime DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        review_data = {
            "review_list": [],
            "review_rate": 0.0,
            "total_followers": 0,
            "reviewed_followers": 0
        }

        if result and result["followers_review_list"]:
            try:
                import json
                review_list = json.loads(result["followers_review_list"])

                if isinstance(review_list, list):
                    review_data["review_list"] = review_list
                    review_data["reviewed_followers"] = len(review_list)

                    # Get total followers count for rate calculation
                    followers_list = await self.get_followers_list(uid)
                    review_data["total_followers"] = len(followers_list)

                    if review_data["total_followers"] > 0:
                        review_data["review_rate"] = (
                            review_data["reviewed_followers"] / review_data["total_followers"]
                        ) * 100

            except (json.JSONDecodeError, TypeError):
                logger.error(f"Failed to parse followers review data for UID={uid}")

        return review_data

    async def get_dahanghai_list(
        self,
        uid: str,
        target_datetime: Optional[datetime] = None
    ) -> List[Dict]:
        """
        Query dahanghai list for a user.

        Args:
            uid: User UID
            target_datetime: Specific datetime to query (defaults to latest)

        Returns:
            List of dahanghai data
        """
        if target_datetime:
            cache_key = f"dahanghai_list_{uid}_{target_datetime.isoformat()}"
            query = """
                SELECT uid, name, dahanghai_list, datetime
                FROM dahanghai_list_table
                WHERE uid = $1 AND datetime <= $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            params = [uid, target_datetime]
        else:
            cache_key = f"dahanghai_list_latest_{uid}"
            query = """
                SELECT uid, name, dahanghai_list, datetime
                FROM dahanghai_list_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            params = [uid]

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=params,
            fetch_type="fetchrow"
        )

        if result and result["dahanghai_list"]:
            try:
                import json
                dahanghai_data = json.loads(result["dahanghai_list"])
                return dahanghai_data if isinstance(dahanghai_data, list) else []
            except (json.JSONDecodeError, TypeError):
                logger.error(f"Failed to parse dahanghai list for UID={uid}")
                return []

        return []

    async def get_video_day_data(
        self,
        uid: str,
        bvid: str,
        recent_days: int = 30
    ) -> List[Dict]:
        """
        Query video day data for a specific video.

        Args:
            uid: User UID
            bvid: Video BVID
            recent_days: Number of recent days to query

        Returns:
            List of daily video statistics
        """
        cache_key = f"video_day_data_{uid}_{bvid}_{recent_days}"

        cutoff_date = datetime.now() - timedelta(days=recent_days)

        query = """
            SELECT uid, name, bvid, video_name, datetime, play_num,
                   comment_num, like_num, coin_num, favorite_num, share_num
            FROM video_day_data_table
            WHERE uid = $1 AND bvid = $2 AND datetime >= $3
            ORDER BY datetime DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, bvid, cutoff_date],
            fetch_type="fetch"
        )

        day_data = []
        if results:
            for row in results:
                day_data.append({
                    "uid": row["uid"],
                    "name": row["name"],
                    "bvid": row["bvid"],
                    "video_name": row["video_name"],
                    "datetime": (
                        row["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                        if row["datetime"]
                        else None
                    ),
                    "play_num": row["play_num"],
                    "comment_num": row["comment_num"],
                    "like_num": row["like_num"],
                    "coin_num": row["coin_num"],
                    "favorite_num": row["favorite_num"],
                    "share_num": row["share_num"],
                })

        return day_data

    async def generate_comment_wordcloud(
        self,
        uid: str,
        limit: int = 1000,
        output_dir: str = "static/wordcloud"
    ) -> Optional[str]:
        """
        Generate word cloud from user comments.

        Args:
            uid: User UID
            limit: Maximum number of comments to process
            output_dir: Directory to save word cloud image

        Returns:
            Path to generated word cloud image or None if failed
        """
        cache_key = f"wordcloud_{uid}_{limit}"

        # Check if cached word cloud exists and is recent
        cached_path = await self.cache.get(cache_key)
        if cached_path and os.path.exists(cached_path):
            # Check if file is less than 1 hour old
            file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(cached_path))
            if file_age < timedelta(hours=1):
                return cached_path

        # Get comments for word cloud
        from vups_server.query.content_queries import user_content_service
        comments = await user_content_service.get_comments_for_wordcloud(uid, limit)

        if not comments:
            logger.warning(f"No comments found for word cloud generation for UID={uid}")
            return None

        try:
            # Import word cloud dependencies
            from wordcloud import WordCloud
            import jieba
            import matplotlib.pyplot as plt

            # Combine all comments
            text = " ".join(comments)

            # Clean text and segment Chinese words
            cleaned_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
            words = jieba.cut(cleaned_text)
            word_text = " ".join(words)

            # Generate word cloud
            wordcloud = WordCloud(
                font_path='simhei.ttf',  # Chinese font
                width=800,
                height=600,
                background_color='white',
                max_words=100,
                colormap='viridis'
            ).generate(word_text)

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wordcloud_{uid}_{timestamp}.png"
            filepath = os.path.join(output_dir, filename)

            # Save word cloud
            plt.figure(figsize=(10, 8))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.tight_layout(pad=0)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

            # Cache the result
            await self.cache.set(cache_key, filepath, ttl=3600)  # 1 hour

            logger.info(f"Generated word cloud for UID={uid}: {filepath}")
            return filepath

        except ImportError:
            logger.error("Word cloud dependencies not installed (wordcloud, jieba, matplotlib)")
            return None
        except Exception as e:
            logger.error(f"Failed to generate word cloud for UID={uid}: {e}")
            return None

    async def cleanup_old_wordcloud_files(
        self,
        output_dir: str = "static/wordcloud",
        max_age_hours: int = 24
    ) -> int:
        """
        Clean up old word cloud files.

        Args:
            output_dir: Directory containing word cloud files
            max_age_hours: Maximum age of files to keep (in hours)

        Returns:
            Number of files deleted
        """
        if not os.path.exists(output_dir):
            return 0

        deleted_count = 0
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)

        try:
            for filename in os.listdir(output_dir):
                if filename.startswith("wordcloud_") and filename.endswith(".png"):
                    filepath = os.path.join(output_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))

                    if file_time < cutoff_time:
                        os.remove(filepath)
                        deleted_count += 1
                        logger.debug(f"Deleted old word cloud file: {filename}")

        except Exception as e:
            logger.error(f"Error cleaning up word cloud files: {e}")

        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} old word cloud files")

        return deleted_count

    async def get_comprehensive_user_info(self, uid: str) -> Dict:
        """
        Get comprehensive user information combining multiple data sources.

        Args:
            uid: User UID

        Returns:
            Dictionary containing comprehensive user information
        """
        cache_key = f"comprehensive_info_{uid}"

        # Try cache first with longer TTL
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # Import other services
        from vups_server.query.user_statistics_queries import user_stats_service
        from vups_server.query.content_queries import user_content_service
        from vups_server.query.analytics_queries import user_analytics_service

        # Gather data from multiple sources
        user_info = await user_content_service.get_user_info_by_uid(uid)
        current_stats = await user_stats_service.get_current_stat_by_uid(uid)
        latest_video = await user_content_service.get_latest_video(uid)
        latest_dynamic = await user_content_service.get_latest_dynamic(uid)
        top_videos = await user_analytics_service.get_top_videos(uid, 5)
        top_dynamics = await user_analytics_service.get_top_dynamics(uid, 5)
        follower_growth = await user_stats_service.calculate_follower_growth_rate(uid)
        dahanghai_growth = await user_stats_service.calculate_dahanghai_growth_rate(uid)

        comprehensive_info = {
            "basic_info": {},
            "current_stats": {},
            "latest_content": {
                "video": latest_video,
                "dynamic": latest_dynamic
            },
            "top_content": {
                "videos": top_videos,
                "dynamics": top_dynamics
            },
            "growth_rates": {
                "follower_growth": follower_growth,
                "dahanghai_growth": dahanghai_growth
            },
            "last_updated": datetime.now().isoformat()
        }

        if user_info:
            comprehensive_info["basic_info"] = {
                "uid": user_info["uid"],
                "name": user_info["name"],
                "face": user_info["face"],
                "sign": user_info["sign"],
                "birthday": user_info["birthday"],
                "top_photo": user_info["top_photo"],
                "room_id": user_info["room_id"],
                "live_url": user_info["live_url"],
            }

        if current_stats:
            comprehensive_info["current_stats"] = {
                "video_total_num": current_stats["video_total_num"],
                "article_total_num": current_stats["article_total_num"],
                "likes_total_num": current_stats["likes_total_num"],
                "elec_num": current_stats["elec_num"],
                "follower_num": current_stats["follower_num"],
                "dahanghai_num": current_stats["dahanghai_num"],
                "datetime": (
                    current_stats["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                    if current_stats["datetime"]
                    else None
                ),
            }

        # Cache with longer TTL for comprehensive data
        await self.cache.set(cache_key, comprehensive_info, ttl=1800)  # 30 minutes

        return comprehensive_info


# Global instance for easy access
user_specialized_service = UserSpecializedQueryService()
