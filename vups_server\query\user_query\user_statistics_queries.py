"""
User statistics query module.
Handles follower counts, dahanghai counts, growth rates, and related statistics.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Union

import asyncpg
from vups.logger import logger
import vups.utils as U
from vups_server.base.query_base import BaseQueryService, QueryBuilder


class UserStatisticsService(BaseQueryService):
    """Service for querying user statistics and growth metrics."""

    def __init__(self):
        super().__init__(cache_ttl=300)  # 5 minutes cache for stats

    async def get_current_stat_by_uid(self, uid: str) -> Optional[asyncpg.Record]:
        """
        Query the latest statistics for a specific user ID.

        Args:
            uid: User UID

        Returns:
            Latest statistics record or None if not found
        """
        cache_key = f"current_stat_{uid}"

        query = """
            SELECT *
            FROM current_stat_table
            WHERE uid = $1
            ORDER BY datetime DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Successfully retrieved latest stats for UID={uid}")
        else:
            logger.info(f"No statistics found for UID={uid}")

        return result

    async def get_user_stats_by_time_range(
        self,
        uid: str,
        start_time: str,
        end_time: str
    ) -> List[List]:
        """
        Query user statistics within a specific time range.

        Args:
            uid: User UID
            start_time: Start time string (YYYY-MM-DD)
            end_time: End time string (YYYY-MM-DD)

        Returns:
            List of formatted statistics data
        """
        start_dt, end_dt = self._format_datetime_range(start_time, end_time)
        if not start_dt or not end_dt:
            return []

        cache_key = f"user_stats_range_{uid}_{start_time}_{end_time}"

        query = """
            SELECT
                TO_CHAR(datetime, 'YYYYMMDDHH24') as datetime_str,
                video_total_num,
                article_total_num,
                likes_total_num,
                elec_num,
                follower_num,
                dahanghai_num
            FROM current_stat_table
            WHERE uid = $1 AND datetime::date >= $2 AND datetime::date <= $3
            ORDER BY datetime ASC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, start_dt.date(), end_dt.date()],
            fetch_type="fetch"
        )

        if results:
            formatted_data = [
                [
                    row["datetime_str"],
                    row["video_total_num"],
                    row["article_total_num"],
                    row["likes_total_num"],
                    row["elec_num"],
                    row["follower_num"],
                    row["dahanghai_num"],
                ]
                for row in results
            ]
            return formatted_data

        return []

    async def get_user_stats_recent_days(
        self,
        uid: str,
        recent_days: int = -1,
        limit: Optional[int] = None
    ) -> List[List]:
        """
        Query user statistics for recent days with optimized performance.

        Args:
            uid: User UID
            recent_days: Number of recent days (-1 for all data)
            limit: Maximum number of records to return

        Returns:
            List of formatted statistics data in chronological order
        """
        cache_key = f"user_stats_recent_{uid}_{recent_days}_{limit}"

        base_query = """
            SELECT
                TO_CHAR(datetime, 'YYYYMMDDHH24') as datetime_str,
                video_total_num,
                article_total_num,
                likes_total_num,
                elec_num,
                follower_num,
                dahanghai_num
            FROM current_stat_table
            WHERE uid = $1
        """

        params = [uid]
        param_count = 1

        if recent_days != -1:
            days_before_time = datetime.now() - timedelta(days=recent_days)
            param_count += 1
            base_query += f" AND datetime >= ${param_count}"
            params.append(days_before_time)

        # Order by DESC first for recent data, then limit
        base_query += " ORDER BY datetime DESC"

        # Apply limit with default for all data to prevent timeouts
        if limit is not None:
            param_count += 1
            base_query += f" LIMIT ${param_count}"
            params.append(limit)
        elif recent_days == -1:
            param_count += 1
            base_query += f" LIMIT ${param_count}"
            params.append(1000)  # Default limit for all data

        results = await self._cached_query(
            cache_key=cache_key,
            query=base_query,
            params=params,
            fetch_type="fetch"
        )

        if results:
            formatted_data = [
                [
                    row["datetime_str"],
                    row["video_total_num"],
                    row["article_total_num"],
                    row["likes_total_num"],
                    row["elec_num"],
                    row["follower_num"],
                    row["dahanghai_num"],
                ]
                for row in results
            ]
            # Reverse to get chronological order (oldest first)
            return list(reversed(formatted_data))

        return []

    async def get_follower_history(
        self,
        uid: str,
        recent_days: int = -1
    ) -> List[List]:
        """
        Query follower number history for a user.

        Args:
            uid: User UID
            recent_days: Number of recent days (-1 for all data)

        Returns:
            List of [datetime_str, follower_num] pairs
        """
        cache_key = f"follower_history_{uid}_{recent_days}"

        base_query = """
            SELECT datetime, follower_num
            FROM current_stat_table
            WHERE uid = $1 AND follower_num IS NOT NULL
        """

        params = [uid]

        if recent_days != -1:
            days_before_time = datetime.now() - timedelta(days=recent_days)
            base_query += " AND datetime >= $2"
            params.append(days_before_time)

        base_query += " ORDER BY datetime DESC"

        results = await self._cached_query(
            cache_key=cache_key,
            query=base_query,
            params=params,
            fetch_type="fetch"
        )

        if results:
            return [
                [row["datetime"].strftime("%Y%m%d%H"), row["follower_num"]]
                for row in results
            ]

        return []

    async def get_current_follower_count(self, uid: str) -> int:
        """
        Query user's current follower count.

        Args:
            uid: User UID

        Returns:
            Current follower count or -1 if not found
        """
        cache_key = f"current_followers_{uid}"

        query = """
            SELECT follower_num
            FROM current_stat_table
            WHERE uid = $1 AND follower_num IS NOT NULL
            ORDER BY datetime DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchval"
        )

        return result if result is not None else -1

    async def get_dahanghai_history(
        self,
        uid: str,
        recent_days: int = -1
    ) -> List[List]:
        """
        Query dahanghai number history for a user.

        Args:
            uid: User UID
            recent_days: Number of recent days (-1 for all data)

        Returns:
            List of [datetime_str, dahanghai_num] pairs
        """
        cache_key = f"dahanghai_history_{uid}_{recent_days}"

        base_query = """
            SELECT datetime, dahanghai_num
            FROM current_stat_table
            WHERE uid = $1 AND dahanghai_num IS NOT NULL
        """

        params = [uid]

        if recent_days != -1:
            days_before_time = datetime.now() - timedelta(days=recent_days)
            base_query += " AND datetime >= $2"
            params.append(days_before_time)

        base_query += " ORDER BY datetime DESC"

        results = await self._cached_query(
            cache_key=cache_key,
            query=base_query,
            params=params,
            fetch_type="fetch"
        )

        if results:
            return [
                [row["datetime"].strftime("%Y%m%d%H"), row["dahanghai_num"]]
                for row in results
            ]

        return []

    async def get_current_dahanghai_count(self, uid: str) -> int:
        """
        Query user's current dahanghai count.

        Args:
            uid: User UID

        Returns:
            Current dahanghai count or -1 if not found
        """
        cache_key = f"current_dahanghai_{uid}"

        query = """
            SELECT dahanghai_num
            FROM current_stat_table
            WHERE uid = $1 AND dahanghai_num IS NOT NULL
            ORDER BY datetime DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchval"
        )

        return result if result is not None else -1

    async def calculate_follower_growth_rate(
        self,
        uid: str,
        recent_days: int = 90
    ) -> str:
        """
        Calculate follower growth rate for a user.

        Args:
            uid: User UID
            recent_days: Time window for growth calculation (days)

        Returns:
            Formatted growth rate string (e.g., "10.5%") or "N/A" or "Error"
        """
        follower_history = await self.get_follower_history(uid, recent_days + 7)

        if not follower_history or len(follower_history) < 2:
            logger.warning(f"Insufficient follower data for UID={uid}, trying longer timeframe...")
            follower_history = await self.get_follower_history(uid, 365)
            if not follower_history or len(follower_history) < 2:
                logger.error(f"Cannot calculate follower growth rate for UID={uid}, insufficient data")
                return "N/A"

        data = []
        for row_data in follower_history:
            try:
                time_dt = datetime.strptime(row_data[0], "%Y%m%d%H")
                num_int = U.safe_int(row_data[1])
                if num_int is not None:
                    data.append((time_dt, num_int))
            except (ValueError, TypeError, IndexError):
                logger.warning(f"Skipping invalid follower data row: {row_data}")
                continue

        if not data:
            logger.error(f"No valid follower data for UID={uid} growth rate calculation")
            return "N/A"

        data.sort(key=lambda x: x[0])
        latest_time, latest_num = data[-1]
        target_past_time = latest_time - timedelta(days=recent_days)

        # Find closest data point to target past time
        days_before_num = None
        closest_diff = timedelta.max

        for time_dt, num_int in reversed(data[:-1]):
            if time_dt <= target_past_time:
                diff = target_past_time - time_dt
                if diff < closest_diff:
                    closest_diff = diff
                    days_before_num = num_int
                if diff == timedelta(0):
                    break

        if days_before_num is None or days_before_num == 0:
            logger.warning(f"No valid past follower data for UID={uid}, cannot calculate growth rate")
            return "N/A"

        try:
            rate = (latest_num - days_before_num) / days_before_num
            return f"{rate * 100:.1f}%"
        except ZeroDivisionError:
            logger.error(f"Division by zero in follower growth rate calculation for UID={uid}")
            return "Error"

    async def calculate_dahanghai_growth_rate(
        self,
        uid: str,
        recent_days: int = 90
    ) -> str:
        """
        Calculate dahanghai growth rate for a user.

        Args:
            uid: User UID
            recent_days: Time window for growth calculation (days)

        Returns:
            Formatted growth rate string (e.g., "10.5%") or "N/A" or "Error"
        """
        dahanghai_history = await self.get_dahanghai_history(uid, recent_days + 7)

        if not dahanghai_history or len(dahanghai_history) < 2:
            logger.warning(f"Insufficient dahanghai data for UID={uid}, trying longer timeframe...")
            dahanghai_history = await self.get_dahanghai_history(uid, 365)
            if not dahanghai_history or len(dahanghai_history) < 2:
                logger.error(f"Cannot calculate dahanghai growth rate for UID={uid}, insufficient data")
                return "N/A"

        data = []
        for row_data in dahanghai_history:
            try:
                time_dt = datetime.strptime(row_data[0], "%Y%m%d%H")
                num_int = U.safe_int(row_data[1])
                if num_int is not None:
                    data.append((time_dt, num_int))
            except (ValueError, TypeError, IndexError):
                logger.warning(f"Skipping invalid dahanghai data row: {row_data}")
                continue

        if not data:
            logger.error(f"No valid dahanghai data for UID={uid} growth rate calculation")
            return "N/A"

        data.sort(key=lambda x: x[0])
        latest_time, latest_num = data[-1]
        target_past_time = latest_time - timedelta(days=recent_days)

        # Find closest data point to target past time
        days_before_num = None
        closest_diff = timedelta.max

        for time_dt, num_int in reversed(data[:-1]):
            if time_dt <= target_past_time:
                diff = target_past_time - time_dt
                if diff < closest_diff:
                    closest_diff = diff
                    days_before_num = num_int
                if diff == timedelta(0):
                    break

        if days_before_num is None or days_before_num == 0:
            logger.warning(f"No valid past dahanghai data for UID={uid}, cannot calculate growth rate")
            return "N/A"

        try:
            rate = (latest_num - days_before_num) / days_before_num
            return f"{rate * 100:.1f}%"
        except ZeroDivisionError:
            logger.error(f"Division by zero in dahanghai growth rate calculation for UID={uid}")
            return "Error"

    async def get_follower_change(self, uid: str, recent_days: int = 1) -> Optional[int]:
        """
        Calculate follower change over specified days.

        Args:
            uid: User UID
            recent_days: Number of days to look back

        Returns:
            Follower change number or None if calculation fails
        """
        today_date = datetime.now().date()
        past_date = today_date - timedelta(days=recent_days)

        # Get today's follower count
        today_query = """
            SELECT follower_num
            FROM current_stat_table
            WHERE uid = $1 AND datetime::date = $2
            ORDER BY datetime DESC
            LIMIT 1
        """
        today_count = await self._execute_query(
            today_query, [uid, today_date], "fetchval"
        )

        # Get past follower count
        past_query = """
            SELECT follower_num
            FROM current_stat_table
            WHERE uid = $1 AND datetime::date <= $2
            ORDER BY datetime DESC
            LIMIT 1
        """
        past_count = await self._execute_query(
            past_query, [uid, past_date], "fetchval"
        )

        if today_count is not None and past_count is not None:
            change = today_count - past_count
            logger.info(f"Follower change for UID={uid}: {change} (today: {today_count}, past: {past_count})")
            return change
        elif today_count is not None:
            logger.warning(f"Only today's follower data available for UID={uid}")
            return today_count
        else:
            logger.warning(f"No follower data available for UID={uid}")
            return None

    async def get_dahanghai_change(self, uid: str, recent_days: int = 1) -> Optional[int]:
        """
        Calculate dahanghai change over specified days.

        Args:
            uid: User UID
            recent_days: Number of days to look back

        Returns:
            Dahanghai change number or None if calculation fails
        """
        today_date = datetime.now().date()
        past_date = today_date - timedelta(days=recent_days)

        # Get today's dahanghai count
        today_query = """
            SELECT dahanghai_num
            FROM current_stat_table
            WHERE uid = $1 AND datetime::date = $2
            ORDER BY datetime DESC
            LIMIT 1
        """
        today_count = await self._execute_query(
            today_query, [uid, today_date], "fetchval"
        )

        # Get past dahanghai count
        past_query = """
            SELECT dahanghai_num
            FROM current_stat_table
            WHERE uid = $1 AND datetime::date <= $2
            ORDER BY datetime DESC
            LIMIT 1
        """
        past_count = await self._execute_query(
            past_query, [uid, past_date], "fetchval"
        )

        if today_count is not None and past_count is not None:
            change = today_count - past_count
            logger.info(f"Dahanghai change for UID={uid}: {change} (today: {today_count}, past: {past_count})")
            return change
        elif today_count is not None:
            logger.warning(f"Only today's dahanghai data available for UID={uid}")
            return today_count
        else:
            logger.warning(f"No dahanghai data available for UID={uid}")
            return None


# Global instance for easy access
user_stats_service = UserStatisticsService()
