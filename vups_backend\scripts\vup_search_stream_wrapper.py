#!/usr/bin/env python3
"""
VUP Search Streaming Wrapper Script
Provides streaming output for VUP search functionality
"""

import sys
import json
import asyncio
import time
from pathlib import Path

# Add the parent directory to Python path to import vups modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from vups.algos.actions.vup_search import vup_searcher
    from vups.logger import logger
except ImportError as e:
    print(json.dumps({
        "type": "error",
        "content": f"Failed to import vups modules: {str(e)}",
        "done": True
    }))
    sys.exit(1)


def stream_output(message, msg_type="data", done=False):
    """Stream a message to stdout"""
    event = {
        "type": msg_type,
        "content": message,
        "done": done
    }
    print(json.dumps(event, ensure_ascii=False))
    sys.stdout.flush()


async def main():
    """Main function to execute VUP search with streaming"""
    if len(sys.argv) < 2:
        stream_output("Missing required argument: question", "error", True)
        sys.exit(1)
    
    question = sys.argv[1]
    
    try:
        # Stream initial status
        stream_output("开始处理VUP查询请求...", "status")
        time.sleep(0.1)
        
        stream_output(f"查询问题: {question}", "info")
        time.sleep(0.1)
        
        stream_output("正在调用VUP搜索引擎...", "status")
        time.sleep(0.1)
        
        # Execute the VUP search
        result = await vup_searcher.run(question)
        
        stream_output("查询完成，正在处理结果...", "status")
        time.sleep(0.1)
        
        # Stream the final result
        stream_output(result, "result", True)
        
    except Exception as e:
        logger.error(f"VUP search streaming error: {str(e)}")
        stream_output(f"查询过程中发生错误: {str(e)}", "error", True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
