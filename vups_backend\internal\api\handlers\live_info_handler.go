package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"vups_backend/internal/models"
	"vups_backend/internal/services"
)

// LiveInfoHandler handles live info requests
type LiveInfoHandler struct {
	service *services.LiveInfoService
}

// NewLiveInfoHandler creates a new live info handler
func NewLiveInfoHandler(service *services.LiveInfoService) *LiveInfoHandler {
	return &LiveInfoHandler{
		service: service,
	}
}

// QueryDanmu queries danmu data
// @Summary Query danmu data
// @Description Query danmu data by room ID and time range
// @Tags Live Info
// @Produce json
// @Param room_id query string true "Room ID"
// @Param start_time query string true "Start time (YYYY-MM-DD or timestamp)"
// @Param end_time query string true "End time (YYYY-MM-DD or timestamp)"
// @Success 200 {object} models.APIResponse{data=[]models.DanmuData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/live/danmu [get]
func (h *LiveInfoHandler) QueryDanmu(c *gin.Context) {
	var request models.LiveInfoRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "danmu"
	h.queryLiveInfo(c, &request)
}

// QuerySuperchat queries superchat data
// @Summary Query superchat data
// @Description Query superchat data by room ID and time range
// @Tags Live Info
// @Produce json
// @Param room_id query string true "Room ID"
// @Param start_time query string true "Start time (YYYY-MM-DD or timestamp)"
// @Param end_time query string true "End time (YYYY-MM-DD or timestamp)"
// @Success 200 {object} models.APIResponse{data=[]models.SuperChatData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/live/superchat [get]
func (h *LiveInfoHandler) QuerySuperchat(c *gin.Context) {
	var request models.LiveInfoRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "superchat"
	h.queryLiveInfo(c, &request)
}

// QueryStatus queries live status data
// @Summary Query live status
// @Description Query live status by room ID and time
// @Tags Live Info
// @Produce json
// @Param room_id query string true "Room ID"
// @Param start_time query string true "Timestamp"
// @Success 200 {object} models.APIResponse{data=models.LiveStatusData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/live/status [get]
func (h *LiveInfoHandler) QueryStatus(c *gin.Context) {
	var request models.LiveInfoRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "status"
	h.queryLiveInfo(c, &request)
}

// QueryCurrentLiveInfo queries current live info
// @Summary Query current live info
// @Description Query current live information by room ID
// @Tags Live Info
// @Produce json
// @Param room_id query string true "Room ID"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/live/current [get]
func (h *LiveInfoHandler) QueryCurrentLiveInfo(c *gin.Context) {
	var request models.LiveInfoRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "current"
	h.queryLiveInfo(c, &request)
}

// QueryAnalytics queries live analytics data
// @Summary Query live analytics
// @Description Query live analytics data by room ID
// @Tags Live Info
// @Produce json
// @Param room_id query string true "Room ID"
// @Success 200 {object} models.APIResponse{data=[]models.LiveAnalyticsData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/live/analytics [get]
func (h *LiveInfoHandler) QueryAnalytics(c *gin.Context) {
	var request models.LiveInfoRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "analytics"
	h.queryLiveInfo(c, &request)
}

// QueryLiveInfoAsync handles async live info requests
// @Summary Async live info query
// @Description Perform an async live info query using Celery
// @Tags Live Info
// @Accept json
// @Produce json
// @Param request body models.LiveInfoRequest true "Live info request"
// @Success 202 {object} models.APIResponse{data=models.TaskStatus}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/live/async [post]
func (h *LiveInfoHandler) QueryLiveInfoAsync(c *gin.Context) {
	var request models.LiveInfoRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	result, err := h.service.QueryLiveInfoAsync(ctx, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to start async query",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, models.APIResponse{
		Code:    http.StatusAccepted,
		Message: "Task started",
		Data:    result,
	})
}

// queryLiveInfo is a common method for handling live info queries
func (h *LiveInfoHandler) queryLiveInfo(c *gin.Context, request *models.LiveInfoRequest) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := h.service.QueryLiveInfo(ctx, request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Query failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    result,
	})
}
