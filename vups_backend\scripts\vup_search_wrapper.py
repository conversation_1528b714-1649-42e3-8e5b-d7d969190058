#!/usr/bin/env python3
"""
VUP Search Wrapper Script
Wraps the existing vup_searcher functionality for Go backend integration
"""

import sys
import json
import asyncio
import os
from pathlib import Path

# Add the parent directory to Python path to import vups modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from vups.algos.actions.vup_search import vup_searcher
    from vups.logger import logger
except ImportError as e:
    print(json.dumps({
        "success": False,
        "error": f"Failed to import vups modules: {str(e)}",
        "data": None
    }))
    sys.exit(1)


async def main():
    """Main function to execute VUP search"""
    if len(sys.argv) < 2:
        print(json.dumps({
            "success": False,
            "error": "Missing required argument: question",
            "data": None
        }))
        sys.exit(1)
    
    question = sys.argv[1]
    
    try:
        # Execute the VUP search
        result = await vup_searcher.run(question)
        
        # Return successful result
        print(json.dumps({
            "success": True,
            "data": {
                "answer": result,
                "question": question
            },
            "error": None
        }, ensure_ascii=False))
        
    except Exception as e:
        logger.error(f"VUP search error: {str(e)}")
        print(json.dumps({
            "success": False,
            "error": str(e),
            "data": None
        }))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
