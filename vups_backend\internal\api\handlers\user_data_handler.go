package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"vups_backend/internal/models"
	"vups_backend/internal/services"
)

// UserDataHandler handles user data requests
type UserDataHandler struct {
	service *services.UserDataService
}

// NewUserDataHandler creates a new user data handler
func NewUserDataHandler(service *services.UserDataService) *UserDataHandler {
	return &UserDataHandler{
		service: service,
	}
}

// GetUserStats gets user statistics
// @Summary Get user statistics
// @Description Get user statistics by UID
// @Tags User Data
// @Produce json
// @Param uid path string true "User UID"
// @Success 200 {object} models.APIResponse{data=models.UserStatsData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/user/{uid}/stats [get]
func (h *UserDataHandler) GetUserStats(c *gin.Context) {
	var request models.UserDataRequest
	if err := c.ShouldBindUri(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid UID parameter",
			Details: err.Error(),
		})
		return
	}

	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "stats"
	h.queryUserData(c, &request)
}

// GetUserContent gets user content (videos and dynamics)
// @Summary Get user content
// @Description Get user content including videos and dynamics by UID
// @Tags User Data
// @Produce json
// @Param uid path string true "User UID"
// @Success 200 {object} models.APIResponse{data=models.UserContentData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/user/{uid}/content [get]
func (h *UserDataHandler) GetUserContent(c *gin.Context) {
	var request models.UserDataRequest
	if err := c.ShouldBindUri(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid UID parameter",
			Details: err.Error(),
		})
		return
	}

	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "content"
	h.queryUserData(c, &request)
}

// GetUserAnalytics gets user analytics data
// @Summary Get user analytics
// @Description Get user analytics data including top comments and videos by UID
// @Tags User Data
// @Produce json
// @Param uid path string true "User UID"
// @Success 200 {object} models.APIResponse{data=models.UserAnalyticsData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/user/{uid}/analytics [get]
func (h *UserDataHandler) GetUserAnalytics(c *gin.Context) {
	var request models.UserDataRequest
	if err := c.ShouldBindUri(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid UID parameter",
			Details: err.Error(),
		})
		return
	}

	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "analytics"
	h.queryUserData(c, &request)
}

// GetUserInfo gets comprehensive user information
// @Summary Get user info
// @Description Get comprehensive user information by UID
// @Tags User Data
// @Produce json
// @Param uid path string true "User UID"
// @Success 200 {object} models.APIResponse{data=models.UserInfoData}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/user/{uid}/info [get]
func (h *UserDataHandler) GetUserInfo(c *gin.Context) {
	var request models.UserDataRequest
	if err := c.ShouldBindUri(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid UID parameter",
			Details: err.Error(),
		})
		return
	}

	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	request.DataType = "info"
	h.queryUserData(c, &request)
}

// GetUserPeriodStats gets user statistics for a specific period
// @Summary Get user period statistics
// @Description Get user statistics for a specific time period by UID
// @Tags User Data
// @Produce json
// @Param uid path string true "User UID"
// @Param start_time query string true "Start time (YYYY-MM-DD)"
// @Param end_time query string true "End time (YYYY-MM-DD)"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/user/{uid}/period-stats [get]
func (h *UserDataHandler) GetUserPeriodStats(c *gin.Context) {
	var request models.UserDataRequest
	if err := c.ShouldBindUri(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid UID parameter",
			Details: err.Error(),
		})
		return
	}

	if err := c.ShouldBindQuery(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid query parameters",
			Details: err.Error(),
		})
		return
	}

	if request.StartTime == "" || request.EndTime == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "start_time and end_time are required for period stats",
		})
		return
	}

	request.DataType = "period_stats"
	h.queryUserData(c, &request)
}

// QueryUserDataAsync handles async user data requests
// @Summary Async user data query
// @Description Perform an async user data query using Celery
// @Tags User Data
// @Accept json
// @Produce json
// @Param request body models.UserDataRequest true "User data request"
// @Success 202 {object} models.APIResponse{data=models.TaskStatus}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/user/async [post]
func (h *UserDataHandler) QueryUserDataAsync(c *gin.Context) {
	var request models.UserDataRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	result, err := h.service.QueryUserDataAsync(ctx, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to start async query",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, models.APIResponse{
		Code:    http.StatusAccepted,
		Message: "Task started",
		Data:    result,
	})
}

// queryUserData is a common method for handling user data queries
func (h *UserDataHandler) queryUserData(c *gin.Context, request *models.UserDataRequest) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	result, err := h.service.QueryUserData(ctx, request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Query failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    result,
	})
}
