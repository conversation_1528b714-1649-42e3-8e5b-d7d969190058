"""
Scheduler Utility Functions

Provides utility functions for scheduler configuration and function management.
"""

import json
import os
from typing import Dict, List, Any, Optional

from vups.logger import logger


def get_scheduler_config() -> Dict[str, Any]:
    """
    Get scheduler configuration from unified_scheduler_config.json
    
    Returns:
        Dictionary containing scheduler configuration
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'unified_scheduler_config.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        return config.get('scheduler', {})
    except Exception as e:
        logger.error(f"Error loading scheduler config: {e}")
        return {
            'timezone': 'Asia/Shanghai',
            'max_workers': 4,
            'function_delay': 5,
            'category_delay': 10,
            'error_retry_count': 3,
            'error_retry_delay': 30
        }


def get_enabled_functions(server_type: str = 'creator') -> Dict[str, List[str]]:
    """
    Get enabled functions for a specific server type from configuration
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        
    Returns:
        Dictionary mapping schedule categories to function lists
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'unified_scheduler_config.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        data_types = config.get('data_types', {})
        server_config = data_types.get(server_type, {})
        
        if not server_config.get('enabled', False):
            logger.warning(f"Server type {server_type} is not enabled in configuration")
            return {}
        
        return server_config.get('functions', {})
        
    except Exception as e:
        logger.error(f"Error loading enabled functions for {server_type}: {e}")
        return {}


def get_function_params(func_name: str) -> Optional[Dict[str, Any]]:
    """
    Get parameters for a specific function
    
    Args:
        func_name: Name of the function
        
    Returns:
        Dictionary of parameters or None if no specific parameters needed
    """
    # Define function-specific parameters
    function_params = {
        'fetch_video_compare': {'size': 20},
        'fetch_video_pandect': {'size': 20},
        'fetch_archive_analyze': {'size': 20},
        'fetch_video_overview': {'size': 20},
        'fetch_fan_graph': {'size': 20},
        'fetch_fan_overview': {'size': 20},
        'fetch_video_survey': {'size': 20},
        # Add more function parameters as needed
    }
    
    return function_params.get(func_name)


def get_all_server_functions() -> Dict[str, Dict[str, List[str]]]:
    """
    Get all functions for all server types
    
    Returns:
        Dictionary mapping server types to their function configurations
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'unified_scheduler_config.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        all_functions = {}
        data_types = config.get('data_types', {})
        
        for server_type, server_config in data_types.items():
            if server_config.get('enabled', False):
                all_functions[server_type] = server_config.get('functions', {})
        
        return all_functions
        
    except Exception as e:
        logger.error(f"Error loading all server functions: {e}")
        return {}


def get_schedule_config(server_type: str) -> Dict[str, Any]:
    """
    Get schedule configuration for a specific server type
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        
    Returns:
        Dictionary containing schedule configuration
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'unified_scheduler_config.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        data_types = config.get('data_types', {})
        server_config = data_types.get(server_type, {})
        
        return server_config.get('schedule', {})
        
    except Exception as e:
        logger.error(f"Error loading schedule config for {server_type}: {e}")
        return {}


def get_limiters_config(server_type: str) -> Dict[str, Any]:
    """
    Get limiters configuration for a specific server type
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        
    Returns:
        Dictionary containing limiters configuration
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'unified_scheduler_config.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        data_types = config.get('data_types', {})
        server_config = data_types.get(server_type, {})
        
        return server_config.get('limiters', {})
        
    except Exception as e:
        logger.error(f"Error loading limiters config for {server_type}: {e}")
        return {}


def validate_function_exists(server_type: str, func_name: str) -> bool:
    """
    Validate that a function exists in the configuration for a server type
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        func_name: Name of the function to validate
        
    Returns:
        True if function exists in configuration, False otherwise
    """
    functions = get_enabled_functions(server_type)
    
    for category_functions in functions.values():
        if func_name in category_functions:
            return True
    
    return False


def get_function_category(server_type: str, func_name: str) -> Optional[str]:
    """
    Get the category (schedule type) for a specific function
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        func_name: Name of the function
        
    Returns:
        Category name or None if function not found
    """
    functions = get_enabled_functions(server_type)
    
    for category, category_functions in functions.items():
        if func_name in category_functions:
            return category
    
    return None


def get_functions_by_category(server_type: str, category: str) -> List[str]:
    """
    Get all functions for a specific category and server type
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        category: Schedule category ('minute', 'hourly', 'daily', etc.)
        
    Returns:
        List of function names
    """
    functions = get_enabled_functions(server_type)
    return functions.get(category, [])


def update_function_config(server_type: str, category: str, func_name: str, action: str = 'add') -> bool:
    """
    Update function configuration (add or remove function from category)
    
    Args:
        server_type: Type of server ('user', 'creator', 'live')
        category: Schedule category
        func_name: Name of the function
        action: 'add' or 'remove'
        
    Returns:
        True if update successful, False otherwise
    """
    try:
        config_path = os.path.join(
            os.path.dirname(__file__), 
            '..', 'config', 'unified_scheduler_config.json'
        )
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        data_types = config.get('data_types', {})
        if server_type not in data_types:
            logger.error(f"Server type {server_type} not found in configuration")
            return False
        
        functions = data_types[server_type].get('functions', {})
        if category not in functions:
            if action == 'add':
                functions[category] = []
            else:
                logger.error(f"Category {category} not found for {server_type}")
                return False
        
        if action == 'add':
            if func_name not in functions[category]:
                functions[category].append(func_name)
        elif action == 'remove':
            if func_name in functions[category]:
                functions[category].remove(func_name)
        else:
            logger.error(f"Invalid action: {action}. Use 'add' or 'remove'")
            return False
        
        # Write back to file
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Successfully {action}ed {func_name} {action == 'add' and 'to' or 'from'} {category} category for {server_type}")
        return True
        
    except Exception as e:
        logger.error(f"Error updating function config: {e}")
        return False
