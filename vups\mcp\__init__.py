from vups.config import VUPS_PROJECT_ROOT
from vups_server.base.cookie_manager import get_cookie_field

def get_mcp_server_dict():
    return {
        "vups-mcp": {
            "command": "uv",
            "args": [
                "--directory",
                str(VUPS_PROJECT_ROOT),
                "run",
                "mcp/server.py"
            ],
            "transport": "stdio",
            "env": {
                "sessdata": get_cookie_field("user", "SESSDATA"),
                "bili_jct": get_cookie_field("user", "bili_jct"),
                "buvid3": get_cookie_field("user", "buvid3"),
            }
        },
        # "sequential-thinking": {
        #     "command": "npx",
        #     "args": [
        #         "-y",
        #         "@modelcontextprotocol/server-sequential-thinking"
        #     ]
        # },
        # "memory": {
        #     "command": "npx",
        #     "args": [
        #         "-y",
        #         "@modelcontextprotocol/server-memory"
        #     ]
        # },
        # "filesystem": {
        #     "command": "npx",
        #     "args": [
        #         "-y",
        #         "@modelcontextprotocol/server-filesystem",
        #         "/Users/<USER>/Desktop",
        #         "/path/to/other/allowed/dir"
        #     ]
        # }
    }
