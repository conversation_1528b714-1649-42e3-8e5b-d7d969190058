package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"vups_backend/internal/models"
)

// SuccessResponse sends a successful response
func SuccessResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    data,
	})
}

// SuccessResponseWithMessage sends a successful response with custom message
func SuccessResponseWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: message,
		Data:    data,
	})
}

// CreatedResponse sends a created response
func CreatedResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusCreated, models.APIResponse{
		Code:    http.StatusCreated,
		Message: "Created",
		Data:    data,
	})
}

// AcceptedResponse sends an accepted response
func AcceptedResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusAccepted, models.APIResponse{
		Code:    http.StatusAccepted,
		Message: "Accepted",
		Data:    data,
	})
}

// BadRequestResponse sends a bad request error response
func BadRequestResponse(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, models.ErrorResponse{
		Code:    http.StatusBadRequest,
		Message: message,
	})
}

// BadRequestResponseWithDetails sends a bad request error response with details
func BadRequestResponseWithDetails(c *gin.Context, message, details string) {
	c.JSON(http.StatusBadRequest, models.ErrorResponse{
		Code:    http.StatusBadRequest,
		Message: message,
		Details: details,
	})
}

// UnauthorizedResponse sends an unauthorized error response
func UnauthorizedResponse(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, models.ErrorResponse{
		Code:    http.StatusUnauthorized,
		Message: message,
	})
}

// ForbiddenResponse sends a forbidden error response
func ForbiddenResponse(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, models.ErrorResponse{
		Code:    http.StatusForbidden,
		Message: message,
	})
}

// NotFoundResponse sends a not found error response
func NotFoundResponse(c *gin.Context, message string) {
	c.JSON(http.StatusNotFound, models.ErrorResponse{
		Code:    http.StatusNotFound,
		Message: message,
	})
}

// InternalServerErrorResponse sends an internal server error response
func InternalServerErrorResponse(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, models.ErrorResponse{
		Code:    http.StatusInternalServerError,
		Message: message,
	})
}

// InternalServerErrorResponseWithDetails sends an internal server error response with details
func InternalServerErrorResponseWithDetails(c *gin.Context, message, details string) {
	c.JSON(http.StatusInternalServerError, models.ErrorResponse{
		Code:    http.StatusInternalServerError,
		Message: message,
		Details: details,
	})
}

// TooManyRequestsResponse sends a too many requests error response
func TooManyRequestsResponse(c *gin.Context, message string) {
	c.JSON(http.StatusTooManyRequests, models.ErrorResponse{
		Code:    http.StatusTooManyRequests,
		Message: message,
	})
}

// PaginatedResponse sends a paginated response
func PaginatedResponse(c *gin.Context, data interface{}, total, page, pageSize int) {
	hasNext := (page * pageSize) < total
	
	response := models.PaginatedResponse{
		Data:     data,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		HasNext:  hasNext,
	}
	
	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}
