package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"vups_backend/internal/config"
)

// Client wraps Redis client with additional functionality
type Client struct {
	rdb *redis.Client
	ctx context.Context
}

// NewClient creates a new Redis client
func NewClient(cfg *config.RedisConfig) (*Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,
		IdleTimeout:  cfg.IdleTimeout,
	})

	ctx := context.Background()
	
	// Test connection
	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &Client{
		rdb: rdb,
		ctx: ctx,
	}, nil
}

// Close closes the Redis connection
func (c *Client) Close() error {
	return c.rdb.Close()
}

// Set stores a key-value pair with expiration
func (c *Client) Set(key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}
	
	return c.rdb.Set(c.ctx, key, data, expiration).Err()
}

// Get retrieves a value by key
func (c *Client) Get(key string, dest interface{}) error {
	data, err := c.rdb.Get(c.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return ErrKeyNotFound
		}
		return fmt.Errorf("failed to get key %s: %w", key, err)
	}
	
	if err := json.Unmarshal([]byte(data), dest); err != nil {
		return fmt.Errorf("failed to unmarshal value: %w", err)
	}
	
	return nil
}

// Delete removes a key
func (c *Client) Delete(key string) error {
	return c.rdb.Del(c.ctx, key).Err()
}

// Exists checks if a key exists
func (c *Client) Exists(key string) (bool, error) {
	count, err := c.rdb.Exists(c.ctx, key).Result()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// SetNX sets a key only if it doesn't exist
func (c *Client) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	data, err := json.Marshal(value)
	if err != nil {
		return false, fmt.Errorf("failed to marshal value: %w", err)
	}
	
	return c.rdb.SetNX(c.ctx, key, data, expiration).Result()
}

// Increment increments a key's value
func (c *Client) Increment(key string) (int64, error) {
	return c.rdb.Incr(c.ctx, key).Result()
}

// IncrementBy increments a key's value by a specific amount
func (c *Client) IncrementBy(key string, value int64) (int64, error) {
	return c.rdb.IncrBy(c.ctx, key, value).Result()
}

// Expire sets expiration for a key
func (c *Client) Expire(key string, expiration time.Duration) error {
	return c.rdb.Expire(c.ctx, key, expiration).Err()
}

// TTL returns the time to live for a key
func (c *Client) TTL(key string) (time.Duration, error) {
	return c.rdb.TTL(c.ctx, key).Result()
}

// Keys returns all keys matching a pattern
func (c *Client) Keys(pattern string) ([]string, error) {
	return c.rdb.Keys(c.ctx, pattern).Result()
}

// FlushDB clears the current database
func (c *Client) FlushDB() error {
	return c.rdb.FlushDB(c.ctx).Err()
}

// Ping tests the connection
func (c *Client) Ping() error {
	return c.rdb.Ping(c.ctx).Err()
}

// Publish publishes a message to a channel
func (c *Client) Publish(channel string, message interface{}) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}
	
	return c.rdb.Publish(c.ctx, channel, data).Err()
}

// Subscribe subscribes to channels
func (c *Client) Subscribe(channels ...string) *redis.PubSub {
	return c.rdb.Subscribe(c.ctx, channels...)
}

// GetClient returns the underlying Redis client
func (c *Client) GetClient() *redis.Client {
	return c.rdb
}

// Cache key generators
func (c *Client) GenerateCacheKey(prefix, id string) string {
	return fmt.Sprintf("%s:%s", prefix, id)
}

func (c *Client) GenerateUserCacheKey(uid string) string {
	return c.GenerateCacheKey("user", uid)
}

func (c *Client) GenerateLiveCacheKey(roomID string) string {
	return c.GenerateCacheKey("live", roomID)
}

func (c *Client) GenerateSearchCacheKey(query string) string {
	return c.GenerateCacheKey("search", query)
}
