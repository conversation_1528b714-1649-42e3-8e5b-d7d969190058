package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"vups_backend/internal/config"
	"vups_backend/internal/models"
	"vups_backend/pkg/celery"
	"vups_backend/pkg/python"
	"vups_backend/pkg/redis"
)

// VUPSearchService handles VUP search operations
type VUPSearchService struct {
	pythonExecutor *python.Executor
	redisClient    *redis.Client
	celeryClient   *celery.Client
	config         *config.Config
}

// NewVUPSearchService creates a new VUP search service
func NewVUPSearchService(
	pythonExecutor *python.Executor,
	redisClient *redis.Client,
	celeryClient *celery.Client,
	config *config.Config,
) *VUPSearchService {
	return &VUPSearchService{
		pythonExecutor: pythonExecutor,
		redisClient:    redisClient,
		celeryClient:   celeryClient,
		config:         config,
	}
}

// Search performs a VUP search query
func (s *VUPSearchService) Search(ctx context.Context, request *models.VUPSearchRequest) (*models.VUPSearchResponse, error) {
	// Check cache first
	cacheKey := s.redisClient.GenerateSearchCacheKey(request.Question)
	var cachedResult models.VUPSearchResponse
	if err := s.redisClient.Get(cacheKey, &cachedResult); err == nil {
		return &cachedResult, nil
	}

	// Execute Python script
	result, err := s.pythonExecutor.VUPSearchExecutor(ctx, request.Question)
	if err != nil {
		return nil, fmt.Errorf("failed to execute VUP search: %w", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("VUP search failed: %s", result.Error)
	}

	// Parse result
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result format")
	}

	answer, ok := data["answer"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid answer format")
	}

	response := &models.VUPSearchResponse{
		Answer: answer,
	}

	// Cache result for 5 minutes
	s.redisClient.Set(cacheKey, response, 5*time.Minute)

	return response, nil
}

// SearchAsync performs an async VUP search query using Celery
func (s *VUPSearchService) SearchAsync(ctx context.Context, request *models.VUPSearchRequest) (*models.VUPSearchResponse, error) {
	// Send task to Celery
	task, err := s.celeryClient.SendVUPSearchTask(request.Question)
	if err != nil {
		return nil, fmt.Errorf("failed to send async task: %w", err)
	}

	response := &models.VUPSearchResponse{
		TaskID: task.ID,
	}

	return response, nil
}

// SearchStream performs a streaming VUP search query
func (s *VUPSearchService) SearchStream(ctx context.Context, request *models.VUPSearchRequest, eventChan chan<- *models.VUPSearchStreamEvent) error {
	defer close(eventChan)

	// Create callback for streaming output
	callback := func(line string) error {
		var event models.VUPSearchStreamEvent
		if err := json.Unmarshal([]byte(line), &event); err != nil {
			// If not JSON, treat as plain text
			event = models.VUPSearchStreamEvent{
				Type:    "data",
				Content: line,
				Done:    false,
			}
		}

		select {
		case eventChan <- &event:
			return nil
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	// Execute streaming Python script
	result, err := s.pythonExecutor.VUPSearchStream(ctx, request.Question, callback)
	if err != nil {
		// Send error event
		errorEvent := &models.VUPSearchStreamEvent{
			Type:    "error",
			Content: err.Error(),
			Done:    true,
		}
		select {
		case eventChan <- errorEvent:
		case <-ctx.Done():
		}
		return err
	}

	if !result.Success {
		// Send error event
		errorEvent := &models.VUPSearchStreamEvent{
			Type:    "error",
			Content: result.Error,
			Done:    true,
		}
		select {
		case eventChan <- errorEvent:
		case <-ctx.Done():
		}
		return fmt.Errorf("VUP search stream failed: %s", result.Error)
	}

	return nil
}

// GetTaskResult gets the result of an async task
func (s *VUPSearchService) GetTaskResult(ctx context.Context, taskID string) (*models.TaskStatus, error) {
	result, err := s.celeryClient.GetTaskResult(taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task result: %w", err)
	}

	status := &models.TaskStatus{
		TaskID: result.TaskID,
		Status: result.Status,
		Result: result.Result,
		Error:  result.Traceback,
	}

	return status, nil
}
