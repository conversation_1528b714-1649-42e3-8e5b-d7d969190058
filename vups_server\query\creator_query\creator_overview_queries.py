"""
Creator overview query module.
Handles daily overview data from key_data_overview_table with time-based filtering.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Union
import time

import asyncpg
from vups.logger import logger
from vups_server.base.query_base import BaseQueryService


class CreatorOverviewService(BaseQueryService):
    """Service for querying creator daily overview data and statistics."""

    def __init__(self):
        super().__init__(cache_ttl=300)  # 5 minutes cache for historical data

    def _date_to_timestamp(self, date_input: Union[str, datetime]) -> int:
        """Convert date to Unix timestamp (bigint format used in database)."""
        if isinstance(date_input, str):
            try:
                dt = datetime.strptime(date_input, '%Y-%m-%d')
            except ValueError:
                logger.error(f"Invalid date format: {date_input}. Expected YYYY-MM-DD")
                return 0
        elif isinstance(date_input, datetime):
            dt = date_input
        else:
            logger.error(f"Invalid date type: {type(date_input)}")
            return 0
        
        return int(dt.timestamp())

    async def get_daily_overview_by_uid_and_date(
        self, 
        uid: str, 
        date: Union[str, datetime]
    ) -> Optional[asyncpg.Record]:
        """
        Get daily overview data for a specific user and date.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object

        Returns:
            Daily overview record or None if not found
        """
        log_date = self._date_to_timestamp(date)
        if not log_date:
            return None

        cache_key = f"daily_overview_{uid}_{log_date}"

        query = """
            SELECT uid, play, play_last, visitor, visitor_last, fan, fan_last,
                   like_count, like_last, fav, fav_last, coin, coin_last,
                   dm, dm_last, comment, comment_last, share, share_last,
                   log_date, create_time, update_time
            FROM key_data_overview_table
            WHERE uid = $1 AND log_date = $2
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, log_date],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved daily overview for UID={uid}, date={date}")
        else:
            logger.info(f"No daily overview found for UID={uid}, date={date}")

        return result

    async def get_overview_data_by_date_range(
        self,
        uid: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime]
    ) -> List[asyncpg.Record]:
        """
        Get daily overview data for a date range.

        Args:
            uid: User UID
            start_date: Start date (inclusive)
            end_date: End date (inclusive)

        Returns:
            List of daily overview records
        """
        start_ts = self._date_to_timestamp(start_date)
        end_ts = self._date_to_timestamp(end_date)
        
        if not start_ts or not end_ts:
            return []

        cache_key = f"overview_range_{uid}_{start_ts}_{end_ts}"

        query = """
            SELECT uid, play, play_last, visitor, visitor_last, fan, fan_last,
                   like_count, like_last, fav, fav_last, coin, coin_last,
                   dm, dm_last, comment, comment_last, share, share_last,
                   log_date, create_time, update_time
            FROM key_data_overview_table
            WHERE uid = $1 AND log_date >= $2 AND log_date <= $3
            ORDER BY log_date ASC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, start_ts, end_ts],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} overview records for UID={uid}, range={start_date} to {end_date}")
        return results or []

    async def get_past_day_data(self, uid: str, days_ago: int = 1) -> Optional[asyncpg.Record]:
        """
        Get overview data for a specific number of days ago.

        Args:
            uid: User UID
            days_ago: Number of days ago (default: 1 for yesterday)

        Returns:
            Daily overview record or None if not found
        """
        target_date = datetime.now() - timedelta(days=days_ago)
        return await self.get_daily_overview_by_uid_and_date(uid, target_date)

    async def get_past_week_overview_summary(self, uid: str, weeks_ago: int = 1) -> List[asyncpg.Record]:
        """
        Get overview data for the past week (7 days).

        Args:
            uid: User UID
            weeks_ago: Number of weeks ago (default: 1 for last week)

        Returns:
            List of daily overview records for the week
        """
        end_date = datetime.now() - timedelta(weeks=weeks_ago)
        start_date = end_date - timedelta(days=6)  # 7 days total
        
        return await self.get_overview_data_by_date_range(uid, start_date, end_date)

    async def get_past_month_overview_summary(self, uid: str, months_ago: int = 1) -> List[asyncpg.Record]:
        """
        Get overview data for the past month (30 days).

        Args:
            uid: User UID
            months_ago: Number of months ago (default: 1 for last month)

        Returns:
            List of daily overview records for the month
        """
        end_date = datetime.now() - timedelta(days=30 * months_ago)
        start_date = end_date - timedelta(days=29)  # 30 days total
        
        return await self.get_overview_data_by_date_range(uid, start_date, end_date)

    async def get_latest_overview_data(self, uid: str) -> Optional[asyncpg.Record]:
        """
        Get the most recent overview data for a user.

        Args:
            uid: User UID

        Returns:
            Latest overview record or None if not found
        """
        cache_key = f"latest_overview_{uid}"

        query = """
            SELECT uid, play, play_last, visitor, visitor_last, fan, fan_last,
                   like_count, like_last, fav, fav_last, coin, coin_last,
                   dm, dm_last, comment, comment_last, share, share_last,
                   log_date, create_time, update_time
            FROM key_data_overview_table
            WHERE uid = $1
            ORDER BY log_date DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved latest overview for UID={uid}")
        else:
            logger.info(f"No overview data found for UID={uid}")

        return result


# Global service instance
creator_overview_service = CreatorOverviewService()
