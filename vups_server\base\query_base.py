"""
Base classes and utilities for database queries.
Provides common patterns, caching mechanisms, and optimized query utilities.
"""

import asyncio
import hashlib
import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
from vups.logger import logger
from vups_server.sql.db_pool import get_connection


class CacheManager:
    """Enhanced caching manager with TTL support and automatic cleanup."""

    def __init__(self, default_ttl: int = 300, max_cache_size: int = 1000):
        self._cache: Dict[str, Tuple[Any, datetime]] = {}
        self._default_ttl = default_ttl
        self._max_cache_size = max_cache_size
        self._lock = asyncio.Lock()

    def _create_cache_key(self, *args, **kwargs) -> str:
        """Create a deterministic cache key from arguments."""
        key_data = f"{args}_{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()

    async def get(self, key: str) -> Optional[Any]:
        """Get cached value if it exists and is not expired."""
        async with self._lock:
            if key in self._cache:
                value, timestamp = self._cache[key]
                if (datetime.now() - timestamp).total_seconds() < self._default_ttl:
                    return value
                else:
                    # Remove expired entry
                    del self._cache[key]
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set cached value with optional custom TTL."""
        async with self._lock:
            # Clean up if cache is too large
            if len(self._cache) >= self._max_cache_size:
                await self._cleanup_old_entries()

            self._cache[key] = (value, datetime.now())

    async def _cleanup_old_entries(self) -> None:
        """Remove oldest entries when cache is full."""
        if not self._cache:
            return

        # Sort by timestamp and remove oldest 20%
        sorted_items = sorted(self._cache.items(), key=lambda x: x[1][1])
        remove_count = max(1, len(sorted_items) // 5)

        for key, _ in sorted_items[:remove_count]:
            del self._cache[key]

    async def clear(self) -> None:
        """Clear all cached entries."""
        async with self._lock:
            self._cache.clear()


class BaseQueryService(ABC):
    """Base class for query services with common functionality."""

    def __init__(self, cache_ttl: int = 300):
        self.cache = CacheManager(default_ttl=cache_ttl)
        self._table_existence_cache: Dict[str, bool] = {}
        self._performance_stats: Dict[str, List[float]] = {}

    async def _execute_query(
        self,
        query: str,
        params: List[Any] = None,
        fetch_type: str = "fetch"
    ) -> Optional[Union[List[asyncpg.Record], asyncpg.Record, Any]]:
        """
        Execute database query with error handling.

        Args:
            query: SQL query string
            params: Query parameters
            fetch_type: Type of fetch operation ('fetch', 'fetchrow', 'fetchval')

        Returns:
            Query results or None if error occurred
        """
        if params is None:
            params = []

        try:
            async with get_connection() as conn:
                if fetch_type == "fetch":
                    return await conn.fetch(query, *params)
                elif fetch_type == "fetchrow":
                    return await conn.fetchrow(query, *params)
                elif fetch_type == "fetchval":
                    return await conn.fetchval(query, *params)
                else:
                    raise ValueError(f"Invalid fetch_type: {fetch_type}")

        except (asyncpg.PostgresError) as e:
            logger.error(f"Database error in {self.__class__.__name__}: {e}")
            return None
        except Exception as ex:
            logger.error(f"Unexpected error in {self.__class__.__name__}: {ex}")
            return None

    async def _cached_query(
        self,
        cache_key: str,
        query: str,
        params: List[Any] = None,
        fetch_type: str = "fetch",
        ttl: Optional[int] = None
    ) -> Optional[Union[List[asyncpg.Record], asyncpg.Record, Any]]:
        """
        Execute query with caching support.

        Args:
            cache_key: Unique cache key
            query: SQL query string
            params: Query parameters
            fetch_type: Type of fetch operation
            ttl: Custom TTL for this cache entry

        Returns:
            Cached or fresh query results
        """
        # Try to get from cache first
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"Cache hit for key: {cache_key[:16]}...")
            return cached_result

        # Execute query and cache result
        result = await self._execute_query(query, params, fetch_type)
        if result is not None:
            await self.cache.set(cache_key, result, ttl)
            logger.debug(f"Cache set for key: {cache_key[:16]}...")

        return result

    async def _execute_enhanced_query(
        self,
        query: str,
        params: List[Any] = None,
        fetch_type: str = "fetch",
        cache_key: Optional[str] = None,
        cache_ttl: Optional[int] = None,
        performance_key: Optional[str] = None
    ) -> Optional[Union[List[asyncpg.Record], asyncpg.Record, Any]]:
        """
        Execute a query with enhanced features: caching, performance monitoring.

        Args:
            query: SQL query string
            params: Query parameters
            fetch_type: Type of fetch operation
            cache_key: Optional cache key for caching results
            cache_ttl: Optional custom cache TTL
            performance_key: Optional key for performance tracking

        Returns:
            Query results or None if error occurred
        """
        start_time = time.time()

        try:
            # Try cache first if cache_key provided
            if cache_key:
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"Cache hit for query: {cache_key[:16]}...")
                    return cached_result

            # Execute query
            result = await self._execute_query(query, params, fetch_type)

            # Cache result if cache_key provided
            if cache_key and result is not None:
                await self.cache.set(cache_key, result, cache_ttl)

            # Track performance
            if performance_key:
                execution_time = time.time() - start_time
                self._track_performance(performance_key, execution_time)

            return result

        except Exception as e:
            logger.error(f"Error executing enhanced query: {e}")
            return None

    async def _check_table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists with caching.

        Args:
            table_name: Name of the table to check

        Returns:
            True if table exists, False otherwise
        """
        # Check cache first
        if table_name in self._table_existence_cache:
            return self._table_existence_cache[table_name]

        try:
            exists = await self._execute_query(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = $1
                );
                """,
                [table_name],
                "fetchval"
            )

            # Cache the result
            self._table_existence_cache[table_name] = bool(exists)
            return bool(exists)

        except Exception as e:
            logger.error(f"Error checking table existence for {table_name}: {e}")
            return False

    def _track_performance(self, key: str, execution_time: float) -> None:
        """
        Track query performance statistics.

        Args:
            key: Performance tracking key
            execution_time: Query execution time in seconds
        """
        if key not in self._performance_stats:
            self._performance_stats[key] = []

        self._performance_stats[key].append(execution_time)

        # Keep only last 100 measurements
        if len(self._performance_stats[key]) > 100:
            self._performance_stats[key] = self._performance_stats[key][-100:]

    def get_performance_stats(self, key: str) -> Dict[str, float]:
        """
        Get performance statistics for a query type.

        Args:
            key: Performance tracking key

        Returns:
            Dictionary with avg, min, max execution times
        """
        if key not in self._performance_stats or not self._performance_stats[key]:
            return {"avg": 0.0, "min": 0.0, "max": 0.0, "count": 0}

        times = self._performance_stats[key]
        return {
            "avg": sum(times) / len(times),
            "min": min(times),
            "max": max(times),
            "count": len(times)
        }

    def _create_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """
        Create a cache key for queries.

        Args:
            prefix: Cache key prefix
            *args: Additional arguments for key generation
            **kwargs: Additional keyword arguments for key generation

        Returns:
            Generated cache key
        """
        key_parts = [prefix] + [str(arg) for arg in args]
        if kwargs:
            key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])

        return self.cache._create_cache_key(*key_parts)

    def _validate_input(self, value: Any, value_name: str, allow_none: bool = False) -> Any:
        """
        Generic input validation.

        Args:
            value: Value to validate
            value_name: Name of the value for error messages
            allow_none: Whether None values are allowed

        Returns:
            Validated value

        Raises:
            ValueError: If validation fails
        """
        if value is None and not allow_none:
            raise ValueError(f"{value_name} cannot be None")

        return value

    def _validate_time_range(
        self,
        start_time: Any,
        end_time: Any,
        time_type: str = "auto"
    ) -> Tuple[Any, Any]:
        """
        Validate time range parameters.

        Args:
            start_time: Start time
            end_time: End time
            time_type: Type of time values ("timestamp", "datetime", "auto")

        Returns:
            Validated (start_time, end_time) tuple

        Raises:
            ValueError: If time range is invalid
        """
        if start_time is None or end_time is None:
            raise ValueError("start_time and end_time cannot be None")

        # Auto-detect time type
        if time_type == "auto":
            if isinstance(start_time, datetime) or isinstance(end_time, datetime):
                time_type = "datetime"
            else:
                time_type = "timestamp"

        # Validate based on type
        if time_type == "datetime":
            if isinstance(start_time, datetime) and isinstance(end_time, datetime):
                if start_time >= end_time:
                    raise ValueError("start_time must be before end_time")
        elif time_type == "timestamp":
            if isinstance(start_time, (int, float)) and isinstance(end_time, (int, float)):
                if start_time >= end_time:
                    raise ValueError("start_time must be before end_time")

        return start_time, end_time

    def _format_query_result(
        self,
        results: Optional[List[asyncpg.Record]],
        default_count: int = 0
    ) -> Tuple[List, int]:
        """
        Format query results into standard tuple format.

        Args:
            results: Query results from database
            default_count: Default count if results is None

        Returns:
            Tuple of (results_list, count)
        """
        if results is None:
            return [], default_count

        return list(results), len(results)

    def clear_table_cache(self) -> None:
        """Clear the table existence cache."""
        self._table_existence_cache.clear()
        logger.info("Table existence cache cleared")

    def clear_performance_stats(self) -> None:
        """Clear performance statistics."""
        self._performance_stats.clear()
        logger.info("Performance statistics cleared")

    def _format_datetime_range(
        self,
        start_time_str: str,
        end_time_str: str
    ) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        Parse and format datetime range strings.

        Args:
            start_time_str: Start time string (YYYY-MM-DD)
            end_time_str: End time string (YYYY-MM-DD)

        Returns:
            Tuple of parsed datetime objects or (None, None) if parsing fails
        """
        try:
            start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
            end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
            return start_time_dt, end_time_dt
        except ValueError:
            logger.error(f"Invalid date format: {start_time_str} or {end_time_str}")
            return None, None

    def _safe_int(self, value: Any) -> int:
        """Safely convert value to integer."""
        try:
            return int(value) if value is not None else 0
        except (ValueError, TypeError):
            return 0

    def _safe_float(self, value: Any) -> float:
        """Safely convert value to float."""
        try:
            return float(value) if value is not None else 0.0
        except (ValueError, TypeError):
            return 0.0


class QueryBuilder:
    """Helper class for building optimized SQL queries."""

    @staticmethod
    def build_time_condition(
        time_column: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        param_offset: int = 1
    ) -> Tuple[str, List[Any]]:
        """
        Build time-based WHERE conditions.

        Args:
            time_column: Name of the datetime column
            start_time: Optional start time filter
            end_time: Optional end time filter
            param_offset: Starting parameter number for SQL placeholders

        Returns:
            Tuple of (condition_string, parameters_list)
        """
        conditions = []
        params = []
        current_param = param_offset

        if start_time:
            conditions.append(f"{time_column} >= ${current_param}")
            params.append(start_time)
            current_param += 1

        if end_time:
            conditions.append(f"{time_column} < ${current_param}")
            params.append(end_time)
            current_param += 1

        condition_str = " AND ".join(conditions)
        return condition_str, params

    @staticmethod
    def build_pagination(
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        param_offset: int = 1
    ) -> Tuple[str, List[Any]]:
        """
        Build LIMIT and OFFSET clauses.

        Args:
            limit: Maximum number of results
            offset: Number of results to skip
            param_offset: Starting parameter number for SQL placeholders

        Returns:
            Tuple of (pagination_string, parameters_list)
        """
        clauses = []
        params = []
        current_param = param_offset

        if limit is not None:
            clauses.append(f"LIMIT ${current_param}")
            params.append(limit)
            current_param += 1

        if offset is not None:
            clauses.append(f"OFFSET ${current_param}")
            params.append(offset)

        pagination_str = " ".join(clauses)
        return pagination_str, params


class PerformanceOptimizer:
    """Utilities for query performance optimization."""

    @staticmethod
    def suggest_indexes(table_name: str, query_patterns: List[Dict[str, Any]]) -> List[str]:
        """
        Suggest database indexes based on query patterns.

        Args:
            table_name: Name of the table
            query_patterns: List of query pattern dictionaries

        Returns:
            List of CREATE INDEX statements
        """
        indexes = []

        for pattern in query_patterns:
            columns = pattern.get('columns', [])
            order_by = pattern.get('order_by', [])
            where_columns = pattern.get('where_columns', [])

            # Create composite indexes for WHERE + ORDER BY patterns
            if where_columns and order_by:
                index_columns = where_columns + [col for col in order_by if col not in where_columns]
                index_name = f"idx_{table_name}_{'_'.join(index_columns)}"
                index_sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({', '.join(index_columns)});"
                indexes.append(index_sql)

        return indexes


# Global cache instances for different query types
user_stats_cache = CacheManager(default_ttl=300)  # 5 minutes
content_cache = CacheManager(default_ttl=600)     # 10 minutes
analytics_cache = CacheManager(default_ttl=1800)  # 30 minutes
