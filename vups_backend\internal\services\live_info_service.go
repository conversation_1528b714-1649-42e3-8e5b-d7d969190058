package services

import (
	"context"
	"fmt"
	"time"

	"vups_backend/internal/config"
	"vups_backend/internal/models"
	"vups_backend/pkg/celery"
	"vups_backend/pkg/python"
	"vups_backend/pkg/redis"
)

// LiveInfoService handles live info operations
type LiveInfoService struct {
	pythonExecutor *python.Executor
	redisClient    *redis.Client
	celeryClient   *celery.Client
	config         *config.Config
}

// NewLiveInfoService creates a new live info service
func NewLiveInfoService(
	pythonExecutor *python.Executor,
	redisClient *redis.Client,
	celeryClient *celery.Client,
	config *config.Config,
) *LiveInfoService {
	return &LiveInfoService{
		pythonExecutor: pythonExecutor,
		redisClient:    redisClient,
		celeryClient:   celeryClient,
		config:         config,
	}
}

// QueryLiveInfo queries live information based on request parameters
func (s *LiveInfoService) QueryLiveInfo(ctx context.Context, request *models.LiveInfoRequest) (interface{}, error) {
	// Check cache first
	cacheKey := s.redisClient.GenerateLiveCacheKey(fmt.Sprintf("%s_%s_%s_%s", 
		request.RoomID, request.StartTime, request.EndTime, request.DataType))
	
	var cachedResult interface{}
	if err := s.redisClient.Get(cacheKey, &cachedResult); err == nil {
		return cachedResult, nil
	}

	// Execute Python script
	result, err := s.pythonExecutor.LiveInfoQuery(ctx, request.RoomID, request.StartTime, request.EndTime, request.DataType)
	if err != nil {
		return nil, fmt.Errorf("failed to execute live info query: %w", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("live info query failed: %s", result.Error)
	}

	// Parse result based on data type
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result format")
	}

	queryResult, ok := data["data"]
	if !ok {
		return nil, fmt.Errorf("invalid data format")
	}

	var response interface{}
	
	switch request.DataType {
	case "danmu":
		response = s.parseDanmuData(queryResult)
	case "superchat":
		response = s.parseSuperChatData(queryResult)
	case "status":
		response = s.parseLiveStatusData(queryResult)
	case "current":
		response = s.parseCurrentLiveInfo(queryResult)
	case "analytics":
		response = s.parseLiveAnalyticsData(queryResult)
	default:
		return nil, fmt.Errorf("unsupported data type: %s", request.DataType)
	}

	// Cache result for 2 minutes
	s.redisClient.Set(cacheKey, response, 2*time.Minute)

	return response, nil
}

// QueryLiveInfoAsync performs an async live info query using Celery
func (s *LiveInfoService) QueryLiveInfoAsync(ctx context.Context, request *models.LiveInfoRequest) (*models.TaskStatus, error) {
	// Send task to Celery
	task, err := s.celeryClient.SendLiveInfoQueryTask(request.RoomID, request.StartTime, request.EndTime, request.DataType)
	if err != nil {
		return nil, fmt.Errorf("failed to send async task: %w", err)
	}

	status := &models.TaskStatus{
		TaskID: task.ID,
		Status: "PENDING",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return status, nil
}

// parseDanmuData parses danmu data from Python result
func (s *LiveInfoService) parseDanmuData(data interface{}) []models.DanmuData {
	var danmuList []models.DanmuData
	
	if dataMap, ok := data.(map[string]interface{}); ok {
		if dataArray, ok := dataMap["data"].([]interface{}); ok {
			for _, item := range dataArray {
				if itemMap, ok := item.(map[string]interface{}); ok {
					danmu := models.DanmuData{
						RoomID:   getString(itemMap, "room_id"),
						UserID:   getString(itemMap, "user_id"),
						UserName: getString(itemMap, "user_name"),
						Message:  getString(itemMap, "message"),
					}
					danmuList = append(danmuList, danmu)
				}
			}
		}
	}
	
	return danmuList
}

// parseSuperChatData parses superchat data from Python result
func (s *LiveInfoService) parseSuperChatData(data interface{}) []models.SuperChatData {
	var superChatList []models.SuperChatData
	
	if dataMap, ok := data.(map[string]interface{}); ok {
		if dataArray, ok := dataMap["data"].([]interface{}); ok {
			for _, item := range dataArray {
				if itemMap, ok := item.(map[string]interface{}); ok {
					superChat := models.SuperChatData{
						RoomID:   getString(itemMap, "room_id"),
						UserID:   getString(itemMap, "user_id"),
						UserName: getString(itemMap, "user_name"),
						Message:  getString(itemMap, "super_chat_msg"),
						Price:    getFloat64(itemMap, "price"),
					}
					superChatList = append(superChatList, superChat)
				}
			}
		}
	}
	
	return superChatList
}

// parseLiveStatusData parses live status data from Python result
func (s *LiveInfoService) parseLiveStatusData(data interface{}) *models.LiveStatusData {
	if dataMap, ok := data.(map[string]interface{}); ok {
		return &models.LiveStatusData{
			RoomID:     getString(dataMap, "room_id"),
			LiveID:     getString(dataMap, "live_id"),
			Status:     getInt(dataMap, "status"),
			Title:      getString(dataMap, "title"),
			Cover:      getString(dataMap, "cover"),
			ParentArea: getString(dataMap, "parent_area"),
			Area:       getString(dataMap, "area"),
		}
	}
	return nil
}

// parseCurrentLiveInfo parses current live info from Python result
func (s *LiveInfoService) parseCurrentLiveInfo(data interface{}) interface{} {
	return data
}

// parseLiveAnalyticsData parses live analytics data from Python result
func (s *LiveInfoService) parseLiveAnalyticsData(data interface{}) []models.LiveAnalyticsData {
	var analyticsList []models.LiveAnalyticsData
	
	if dataArray, ok := data.([]interface{}); ok {
		for _, item := range dataArray {
			if itemMap, ok := item.(map[string]interface{}); ok {
				analytics := models.LiveAnalyticsData{
					LiveID:              getString(itemMap, "live_id"),
					DanmuCount:          getInt(itemMap, "danmu_count"),
					Income:              getFloat64(itemMap, "income"),
					WatchChangeCount:    getInt(itemMap, "watch_change_count"),
					PayCount:            getInt(itemMap, "pay_count"),
					InteractionCount:    getInt(itemMap, "interaction_count"),
					MaxOnlineRank:       getInt(itemMap, "max_online_rank"),
					EnterRoomCount:      getInt(itemMap, "enter_room_count"),
					AverageOnlineRank:   getInt(itemMap, "average_online_rank"),
					AverageEnterRoom:    getInt(itemMap, "average_enter_room"),
				}
				analyticsList = append(analyticsList, analytics)
			}
		}
	}
	
	return analyticsList
}

// Helper functions for type conversion
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func getInt(m map[string]interface{}, key string) int {
	if val, ok := m[key]; ok {
		if num, ok := val.(float64); ok {
			return int(num)
		}
		if num, ok := val.(int); ok {
			return num
		}
	}
	return 0
}

func getFloat64(m map[string]interface{}, key string) float64 {
	if val, ok := m[key]; ok {
		if num, ok := val.(float64); ok {
			return num
		}
		if num, ok := val.(int); ok {
			return float64(num)
		}
	}
	return 0.0
}
