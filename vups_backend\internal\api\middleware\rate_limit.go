package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
	"vups_backend/internal/config"
	"vups_backend/internal/models"
)

// RateLimiter represents a rate limiter
type RateLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	rate     rate.Limit
	burst    int
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(cfg *config.RateLimitConfig) *RateLimiter {
	return &RateLimiter{
		limiters: make(map[string]*rate.Limiter),
		rate:     rate.Limit(cfg.Rate),
		burst:    cfg.Burst,
	}
}

// getLimiter gets or creates a limiter for the given key
func (rl *RateLimiter) getLimiter(key string) *rate.Limiter {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	
	limiter, exists := rl.limiters[key]
	if !exists {
		limiter = rate.NewLimiter(rl.rate, rl.burst)
		rl.limiters[key] = limiter
	}
	
	return limiter
}

// cleanupLimiters removes old limiters periodically
func (rl *RateLimiter) cleanupLimiters() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		rl.mu.Lock()
		for key, limiter := range rl.limiters {
			// Remove limiters that haven't been used recently
			if limiter.Tokens() == float64(rl.burst) {
				delete(rl.limiters, key)
			}
		}
		rl.mu.Unlock()
	}
}

// RateLimitMiddleware creates a rate limiting middleware
func RateLimitMiddleware(cfg *config.RateLimitConfig) gin.HandlerFunc {
	if !cfg.Enabled {
		return func(c *gin.Context) {
			c.Next()
		}
	}
	
	rateLimiter := NewRateLimiter(cfg)
	
	// Start cleanup goroutine
	go rateLimiter.cleanupLimiters()
	
	return func(c *gin.Context) {
		// Use client IP as the key for rate limiting
		key := c.ClientIP()
		
		limiter := rateLimiter.getLimiter(key)
		
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, models.ErrorResponse{
				Code:    http.StatusTooManyRequests,
				Message: "Rate limit exceeded",
				Details: "Too many requests from this IP address",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RateLimitByUserMiddleware creates a rate limiting middleware by user
func RateLimitByUserMiddleware(cfg *config.RateLimitConfig) gin.HandlerFunc {
	if !cfg.Enabled {
		return func(c *gin.Context) {
			c.Next()
		}
	}
	
	rateLimiter := NewRateLimiter(cfg)
	
	// Start cleanup goroutine
	go rateLimiter.cleanupLimiters()
	
	return func(c *gin.Context) {
		// Try to get user ID from header or use IP as fallback
		userID := c.GetHeader("X-User-ID")
		if userID == "" {
			userID = c.ClientIP()
		}
		
		limiter := rateLimiter.getLimiter(userID)
		
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, models.ErrorResponse{
				Code:    http.StatusTooManyRequests,
				Message: "Rate limit exceeded",
				Details: "Too many requests from this user",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// RateLimitByEndpointMiddleware creates endpoint-specific rate limiting
func RateLimitByEndpointMiddleware(endpointLimits map[string]*config.RateLimitConfig) gin.HandlerFunc {
	limiters := make(map[string]*RateLimiter)
	
	for endpoint, cfg := range endpointLimits {
		if cfg.Enabled {
			limiters[endpoint] = NewRateLimiter(cfg)
			go limiters[endpoint].cleanupLimiters()
		}
	}
	
	return func(c *gin.Context) {
		path := c.FullPath()
		
		rateLimiter, exists := limiters[path]
		if !exists {
			c.Next()
			return
		}
		
		key := c.ClientIP()
		limiter := rateLimiter.getLimiter(key)
		
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, models.ErrorResponse{
				Code:    http.StatusTooManyRequests,
				Message: "Rate limit exceeded for this endpoint",
				Details: "Too many requests to this endpoint",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}
