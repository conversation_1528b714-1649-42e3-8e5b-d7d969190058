# Async Subtitle Generation Usage Guide

## Overview

The new async subtitle generation system solves FastMCP timeout issues by processing long-running ASR tasks in the background while providing real-time progress updates.

## Quick Start

### 1. Start a Subtitle Task

```python
# MCP Tool Call
result = await start_video_subtitle_task("BV1234567890")
# Returns: {"task_id": "uuid-string", "status": "pending", "message": "..."}
task_id = result["task_id"]
```

### 2. Monitor Progress

```python
# Check progress periodically
while True:
    status = await get_subtitle_task_status(task_id)
    print(f"Progress: {status['progress_percent']}% - {status['current_step']}")
    
    if status["status"] == "completed":
        break
    elif status["status"] == "failed":
        print(f"Error: {status['error_message']}")
        break
    
    await asyncio.sleep(5)  # Check every 5 seconds
```

### 3. Get Results

```python
# Once completed, retrieve the result
result = await get_subtitle_task_result(task_id)
subtitle_text = result["result"]
```

## API Reference

### start_video_subtitle_task(bvid, output_filename=None)

**Purpose**: Start background subtitle generation for a Bilibili video

**Parameters**:
- `bvid` (str): Bilibili video ID (e.g., "BV1234567890")
- `output_filename` (str, optional): Custom output file path

**Returns**:
```json
{
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "pending",
    "message": "Subtitle generation task created for video BV1234567890"
}
```

### get_subtitle_task_status(task_id)

**Purpose**: Check current task status and progress

**Parameters**:
- `task_id` (str): Task ID returned from start_video_subtitle_task

**Returns**:
```json
{
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "transcribing",
    "progress_percent": 65.5,
    "current_step": "Processing audio chunk 650/1000",
    "estimated_remaining": 120,
    "error_message": null,
    "created_at": "2024-01-15T10:30:00",
    "updated_at": "2024-01-15T10:35:30"
}
```

**Status Values**:
- `pending`: Task queued for processing
- `downloading`: Downloading video/audio
- `transcribing`: Running ASR processing
- `completed`: Task finished successfully
- `failed`: Task encountered an error
- `cancelled`: Task was cancelled by user

### get_subtitle_task_result(task_id)

**Purpose**: Retrieve results from completed task

**Parameters**:
- `task_id` (str): Task ID

**Returns** (when completed):
```json
{
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "result": "完整的字幕文本内容...",
    "completed_at": "2024-01-15T10:40:00"
}
```

**Returns** (when not completed):
```json
{
    "error": "Task not completed. Current status: transcribing",
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "transcribing"
}
```

### cancel_subtitle_task(task_id)

**Purpose**: Cancel a running task

**Parameters**:
- `task_id` (str): Task ID to cancel

**Returns**:
```json
{
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "cancelled",
    "message": "Task cancelled successfully"
}
```

## Progress Tracking Details

### Progress Stages

1. **Task Creation** (0%): Task queued
2. **Video Analysis** (10%): Checking for existing subtitles
3. **Audio Download** (10-30%): Downloading video audio
4. **ASR Processing** (30-90%): Transcribing audio to text
5. **Finalization** (90-100%): Saving results

### Real-time Updates

The system provides real-time progress updates during ASR processing:
- Chunk-based progress for streaming transcription
- Estimated remaining time when available
- Detailed status messages for each processing step

## Error Handling

### Common Error Scenarios

1. **Video Not Found**: Invalid BV ID
2. **Download Failed**: Network issues or restricted video
3. **ASR Failed**: Audio processing errors
4. **Timeout**: Extremely long videos (rare with chunked processing)

### Error Response Format

```json
{
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "failed",
    "error_message": "Failed to download audio: Video not accessible",
    "progress_percent": 25.0,
    "current_step": "Audio download failed"
}
```

## Best Practices

### For Short Videos (< 10 minutes)
- Can still use original `get_video_subtitle()` for immediate results
- Async approach provides better user experience with progress feedback

### For Long Videos (> 10 minutes)
- **Always** use async approach to avoid timeouts
- Poll status every 5-10 seconds for optimal balance
- Implement client-side timeout (e.g., 30 minutes max)

### For Production Use
- Store task IDs for user session management
- Implement task cleanup for old completed tasks
- Add retry logic for failed tasks
- Consider persistent storage for task state

## Migration Guide

### From Synchronous to Asynchronous

**Old Code**:
```python
result = await get_video_subtitle("BV1234567890")
print(result)
```

**New Code**:
```python
# Start task
task_result = await start_video_subtitle_task("BV1234567890")
task_id = task_result["task_id"]

# Monitor progress
while True:
    status = await get_subtitle_task_status(task_id)
    if status["status"] == "completed":
        result = await get_subtitle_task_result(task_id)
        print(result["result"])
        break
    await asyncio.sleep(5)
```

## Performance Characteristics

- **Task Creation**: < 100ms (immediate response)
- **Progress Updates**: Every 10 audio chunks (~1-2 seconds)
- **Memory Usage**: Minimal overhead per task
- **Concurrent Tasks**: Supports multiple simultaneous tasks
- **Scalability**: Limited by ASR API rate limits, not system resources

## Troubleshooting

### Task Stuck in "pending"
- Check if task manager is running
- Verify ASR API credentials
- Check system resources

### No Progress Updates
- Ensure progress callbacks are properly configured
- Check for ASR streaming mode compatibility

### High Memory Usage
- Implement task cleanup for old tasks
- Consider persistent storage for large deployments
