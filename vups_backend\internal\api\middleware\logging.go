package middleware

import (
	"time"

	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoggingMiddleware creates a logging middleware
func LoggingMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// Get request ID from request headers
		requestID := param.Request.Header.Get("X-Request-ID")

		// Log the request
		logger.WithFields(logrus.Fields{
			"request_id":    requestID,
			"client_ip":     param.ClientIP,
			"method":        param.Method,
			"path":          param.Path,
			"status_code":   param.StatusCode,
			"latency":       param.Latency,
			"user_agent":    param.Request.UserAgent(),
			"error_message": param.ErrorMessage,
		}).Info("HTTP Request")

		return ""
	})
}

// StructuredLoggingMiddleware creates a structured logging middleware
func StructuredLoggingMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get request ID
		requestID := requestid.Get(c)

		// Prepare log fields
		fields := logrus.Fields{
			"request_id":  requestID,
			"client_ip":   c.ClientIP(),
			"method":      c.Request.Method,
			"path":        path,
			"status_code": c.Writer.Status(),
			"latency":     latency,
			"user_agent":  c.Request.UserAgent(),
		}

		if raw != "" {
			fields["query"] = raw
		}

		// Add error message if exists
		if len(c.Errors) > 0 {
			fields["error"] = c.Errors.String()
		}

		// Log based on status code
		switch {
		case c.Writer.Status() >= 500:
			logger.WithFields(fields).Error("Server Error")
		case c.Writer.Status() >= 400:
			logger.WithFields(fields).Warn("Client Error")
		case c.Writer.Status() >= 300:
			logger.WithFields(fields).Info("Redirection")
		default:
			logger.WithFields(fields).Info("Success")
		}
	}
}
