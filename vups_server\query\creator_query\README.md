# Creator Query Modules

This directory contains database query modules for creator data operations, implementing the database operations defined in `vups_server/sql/sentence/creator_sql.py`.

## Module Overview

The creator query modules are designed to:
- Retrieve historical data collections for specific time periods (day/week/month)
- Follow existing codebase patterns for database query modules
- Use SQL statements from `creator_sql.py` as the foundation
- Include proper error handling and follow single responsibility principle
- Support querying data for past periods with configurable date ranges

## Module Structure

### 1. `creator_overview_queries.py`
Handles daily overview data from `key_data_overview_table`.

**Key Functions:**
- `get_daily_overview_by_uid_and_date()` - Get data for a specific date
- `get_overview_data_by_date_range()` - Get data for a date range
- `get_past_day_data()` - Get data for N days ago
- `get_past_week_overview_summary()` - Get daily data for past week
- `get_past_month_overview_summary()` - Get daily data for past month
- `get_latest_overview_data()` - Get most recent data

### 2. `creator_weekly_queries.py`
Handles weekly metrics from `weekly_metrics_table` and related views.

**Key Functions:**
- `get_weekly_metrics_by_uid_and_date()` - Get raw weekly metrics
- `get_weekly_overview_data()` - Get overview using view
- `get_weekly_play_analyze_data()` - Get play analysis data
- `get_weekly_attention_analyze_data()` - Get attention analysis data
- `get_past_week_data()` - Get data for N weeks ago
- `get_weekly_data_by_date_range()` - Get data for date range
- `get_weekly_archive_analyze_data()` - Get archive analysis data

### 3. `creator_video_queries.py`
Handles video-related data from `key_video_compare_table` and `key_video_archive_table`.

**Key Functions:**
- `get_video_compare_data_by_uid_and_date()` - Get video comparison data
- `get_video_compare_data_by_date_range()` - Get data for date range
- `get_video_archive_data_by_uid()` - Get video archive data
- `get_video_archive_data_by_bvid()` - Get specific video data
- `get_past_month_video_data()` - Get data for past month
- `get_recent_video_performance()` - Get recent performance data
- `get_video_elec_num_data()` - Get electric number data
- `get_latest_video_elec_num()` - Get latest electric numbers

### 4. `creator_analytics_queries.py`
Provides comprehensive data collection and analytics functions.

**Key Functions:**
- `get_comprehensive_day_data()` - Complete data for a specific day
- `get_comprehensive_week_data()` - Complete data for a specific week
- `get_comprehensive_month_data()` - Complete data for a specific month
- `get_data_by_configurable_range()` - Flexible date range queries

## Usage Examples

### Basic Usage

```python
from vups_server.query.creator_query import (
    creator_overview_service,
    creator_weekly_service,
    creator_video_service,
    creator_analytics_service
)

# Get daily overview data
daily_data = await creator_overview_service.get_daily_overview_by_uid_and_date(
    uid="123456", 
    date="2024-01-15"
)

# Get weekly metrics
weekly_data = await creator_weekly_service.get_weekly_overview_data(
    uid="123456",
    date="2024-01-15"
)

# Get video data
video_data = await creator_video_service.get_video_archive_data_by_uid(
    uid="123456",
    limit=50
)
```

### Comprehensive Data Collection

```python
# Get complete data for a specific past day
day_data = await creator_analytics_service.get_comprehensive_day_data(
    uid="123456",
    date="2024-01-15"
)

# Get complete data for a specific past week
week_data = await creator_analytics_service.get_comprehensive_week_data(
    uid="123456",
    week_start_date="2024-01-08"
)

# Get complete data for a specific past month
month_data = await creator_analytics_service.get_comprehensive_month_data(
    uid="123456",
    month_start_date="2024-01-01"
)
```

### Configurable Date Range Queries

```python
# Get data for custom date range
range_data = await creator_analytics_service.get_data_by_configurable_range(
    uid="123456",
    start_date="2024-01-01",
    end_date="2024-01-31",
    include_video_data=True,
    include_weekly_data=True
)

# Get overview data for specific range
overview_range = await creator_overview_service.get_overview_data_by_date_range(
    uid="123456",
    start_date="2024-01-01",
    end_date="2024-01-07"
)
```

## Data Structures

### Daily Overview Data
Contains metrics like play count, visitor count, fan count, likes, etc. with both current and last period values.

### Weekly Metrics Data
Normalized structure with report types:
- `overview` - General overview metrics
- `play_analyze` - Play analysis metrics
- `attention_analyze` - Attention analysis metrics

### Video Data
Includes video comparison metrics and archive data with performance statistics.

## Error Handling

All modules follow consistent error handling patterns:
- Invalid date formats return empty results or None
- Database errors are logged and return None/empty lists
- Input validation for UIDs and date parameters
- Graceful handling of missing data

## Caching

- Overview data: 5 minutes TTL
- Weekly data: 5 minutes TTL
- Video data: 5 minutes TTL
- Analytics data: 10 minutes TTL

## Database Tables Used

- `key_data_overview_table` - Daily overview metrics
- `weekly_metrics_table` - Normalized weekly metrics
- `weekly_key_data_overview_table` - Weekly overview view
- `weekly_play_analyze_table` - Weekly play analysis view
- `weekly_attention_analyze_table` - Weekly attention analysis view
- `weekly_archive_analyze_table` - Weekly archive analysis
- `key_video_compare_table` - Video comparison metrics
- `key_video_archive_table` - Video archive data
- `key_elec_num_table` - Electric number data

## Integration

These modules integrate with the existing query infrastructure:
- Inherit from `BaseQueryService` for common functionality
- Use connection pooling from `vups_server.sql.db_pool`
- Follow logging patterns with `vups.logger`
- Support caching with configurable TTL values
