import asyncio
from langchain_mcp_adapters.tools import load_mcp_tools
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from vups.base.llm import LLM_DICT
from vups.config import VUPS_PROJECT_ROOT
from vups_server.base.cookie_manager import get_cookie_field


async def main():
    client = MultiServerMCPClient({
        "vups-mcp": {
            "command": "uv",
            "args": [
                "--directory",
                str(VUPS_PROJECT_ROOT),
                "run",
                "mcp/server.py"
            ],
            "transport": "stdio",
            "env": {
                "sessdata": get_cookie_field("user", "SESSDATA"),
                "bili_jct": get_cookie_field("user", "bili_jct"),
                "buvid3": get_cookie_field("user", "buvid3"),
            }
        }
    })
    async with client.session("vups-mcp") as session:
        tools = await load_mcp_tools(session)
        agent = create_react_agent(LLM_DICT["hunyuan-standard-256K"], tools)
        math_response = await agent.ainvoke({"messages": "星瞳的的最新粉丝数和最新视频是什么"})
        print(math_response)
        print(math_response["messages"][1].content)
        print(math_response["messages"][1].tool_calls[0]["name"])
        print(math_response["messages"][1].tool_calls[0]["args"])
        print(math_response["messages"][2].content)
        print(math_response["messages"][2].name)

# response answer
# json = {
#   "messages": [
#     {
#       "type": "HumanMessage",
#       "content": "星瞳最新视频是什么",
#       "additional_kwargs": {},
#       "response_metadata": {},
#       "id": "3ce0345e-ee30-47bb-9b29-b2f543d841c8"
#     },
#     {
#       "type": "AIMessage",
#       "content": "用户想要知道星瞳最新的视频。我需要调用search_video工具来搜索星瞳的最新视频。\n\t\n\t调用search_video工具来搜索星瞳的最新视频。",
#       "additional_kwargs": {
#         "tool_calls": [
#           {
#             "id": "call_d2omk9c2c3mfrjfd4bj0",
#             "function": {
#               "arguments": "{\"keyword\":\"星瞳\"}",
#               "name": "search_video"
#             },
#             "type": "function",
#             "index": 0
#           }
#         ],
#         "refusal": null
#       },
#       "response_metadata": {
#         "token_usage": {
#           "completion_tokens": 42,
#           "prompt_tokens": 6,
#           "total_tokens": 48,
#           "completion_tokens_details": null,
#           "prompt_tokens_details": null
#         },
#         "model_name": "hunyuan-standard-256K",
#         "system_fingerprint": null,
#         "id": "3f6a3f8a35994da6a8ed997c9811eb5a",
#         "service_tier": null,
#         "finish_reason": "tool_calls",
#         "logprobs": null
#       },
#       "id": "run--1a1eb921-792f-4dcf-8c6f-c82385ec063c-0",
#       "tool_calls": [
#         {
#           "name": "search_video",
#           "args": {
#             "keyword": "星瞳"
#           },
#           "id": "call_d2omk9c2c3mfrjfd4bj0",
#           "type": "tool_call"
#         }
#       ],
#       "usage_metadata": {
#         "input_tokens": 6,
#         "output_tokens": 42,
#         "total_tokens": 48,
#         "input_token_details": {},
#         "output_token_details": {}
#       }
#     },
#     {
#       "type": "ToolMessage",
#       "content": "| 发布日期       | 标题                                                                                                                                                                                                 | UP主           | 时长     | 播放量  | 点赞数  | 类别    | bvid         |\n|:-----------|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|:---------------|:---------|:--------|:--------|:--------|:-------------|\n| 2025/08/24 | [【柚恩】真压抑了！杭州癔症姐看<em class=\"keyword\">星瞳</em>《大展宏图》整个人都软了：<em class=\"keyword\">星瞳</em>嘴皮子好快，我这个铁血小星星好喜欢�](http://www.bilibili.com/video/av115083883580074) | 珈蓝梦         | 3:29    | 11450   | 237     | 动画综合  | BV1TxeHzyEyL |\n| 2024/10/06 | [【<em class=\"keyword\">星瞳</em>】正面回应大家近期关注的关于xt信息的各种问题](http://www.bilibili.com/video/av113260502190101)                                                                 | 星瞳的发病小棉袄    | 10:53   | 195521  | 1827    | 动画综合  | BV1Zz1BYfEoj |\n| 2025/08/22 | [<em class=\"keyword\">星瞳</em>前头号切片员恨也迷人风波详解！一切竟是一个切片引发的血案？李姐觉醒女性力量、三姐暂停直播、露早买狗被商家反呛！ 【V圈史话2025】第四十三回](http://www.bilibili.com/video/av115068096353508)   | 悠木皆守        | 7:48    | 143300  | 4185    | 手机游戏  | BV139YRzrEaV |\n| 2024/08/30 | [【<em class=\"keyword\">星瞳</em>】<em class=\"keyword\">星瞳</em>ASMR十四分钟纯享版（建议收藏）](http://www.bilibili.com/video/av113051206484882)                                                         | 星瞳的发病小棉袄    | 14:16   | 50773   | 883     | 动画综合  | BV1VbHEepEgc |\n| 2025/08/29 | [【雪糕cheese】腾讯游戏她不一样，腾讯有<em class=\"keyword\">星瞳</em>和<em class=\"keyword\">星瞳</em>的大米](http://www.bilibili.com/video/av115111096228696)                     | 白熊咖啡师       | 2:19    | 1427    | 31      | 桌游棋牌  | BV1qwh9zzEKT |\n| 2025/08/29 | [<em class=\"keyword\">星瞳</em>的法修散打～](http://www.bilibili.com/video/av115109804508569)                                                                                            | 米需米卷土重来     | 0:18    | 984     | 181     | 仿妆cos | BV1DShQzfECQ |\n| 2021/10/29 | [【暖暖x鹿鸣x<em class=\"keyword\">星瞳</em>】梦 幻 联 动! | 每 天 一 遍，烦 恼 再 见 !](http://www.bilibili.com/video/av763845604)                                                                 | 独角仙の小谷屋     | 1:59    | 287068  | 9890    | GMV     | BV1pr4y117k8 |\n| 2025/08/12 | [盘点虚环的角色<em class=\"keyword\">星瞳</em>在现实中是什么样的【<em class=\"keyword\">星瞳</em>】](http://www.bilibili.com/video/av115013754817625)                                             | 欧气满满的小星星    | 2:54    | 32592   | 831     | 动画综合  | BV1WotQzVEZh |\n| 2025/08/18 | [洛天依！你的兵来了！！！【<em class=\"keyword\">星瞳</em>vlog】](http://www.bilibili.com/video/av115049406465535)                                                                    | 星瞳_Official | 4:24    | 54015   | 7877    | 日常    | BV1dQY4zMEym |\n| 2024/04/19 | [这下真看到星屯了 想不到腾讯这么敬业 里面都建模了【<em class=\"keyword\">星瞳</em>】](http://www.bilibili.com/video/av1953498961)                                                    | 恨也迷人        | 2:43    | 412858  | 5383    | 动画综合  | BV1sC411n7YC |\n| 2020/12/10 | [<em class=\"keyword\">星瞳</em>dj完整版，视频2分钟抖腿一整天](http://www.bilibili.com/video/av755527216)                                                                                    | 想陪你看风景      | 2:1     | 820405  | 16766   | 动画综合  | BV1A64y1f7vJ |\n| 2025/08/29 | [雪糕被问爱瞳姐还是爱大米，雪糕:我都爱](http://www.bilibili.com/video/av115110777462008)                                                                                                  | 清歌不改名       | 2:6     | 234     | 11      | 桌游棋牌  | BV1Hhh9zSE9T |\n| 2025/08/25 | [【<em class=\"keyword\">星瞳</em>】从夯到拉锐评<em class=\"keyword\">星瞳</em>所有大活](http://www.bilibili.com/video/av115089118070255)                                           | 星瞳的发病小棉袄    | 2:48    | 7530    | 364     | 动画综合  | BV1vveozsE4M |\n| 2022/04/26 | [【<em class=\"keyword\">星瞳</em>】马化腾女儿进腾讯也要面试？腾讯招聘拷打<em class=\"keyword\">星瞳</em>30分钟实录！](http://www.bilibili.com/video/av596058392)                         | 2568号小星星    | 34:26   | 103719  | 1678    | 日常    | BV1hB4y1m7C6 |\n| 2025/06/13 | [【充电回放】<em class=\"keyword\">星瞳</em>首场线下演唱会舞台丨导演剪辑版](http://www.bilibili.com/video/av114675073158329)                                           | 星瞳_Official | 138:50  | 108628  | 3521    | 动画综合  | BV1cMMgzTEBP |\n| 2025/08/28 | [赛博麻沸散！七海阿梓雪糕KTV激情合唱瞳姐《猫舞喵呜》！给路过的服务员小哥听傻了【糕小海梓】](http://www.bilibili.com/video/av115107036203137)                 | 星瞳的发病小棉袄    | 3:56    | 5359    | 163     | 动画综合  | BV1V5h2zEEu1 |\n| 2025/08/29 | [<em class=\"keyword\">星瞳</em>说的绝对领域，是黑色过膝袜](http://www.bilibili.com/video/av115108025997740)                                                                             | 好事花生_Peanut | 1:4     | 395     | 19      | 日常    | BV19EhUzpEU7 |\n| 2021/01/13 | [【1080p超清】【<em class=\"keyword\">星瞳</em>➕燕无歇➕同类最强踩点】要个十万播放不过分吧！](http://www.bilibili.com/video/av586187717)                                           | 账号已注销       | 2:3     | 107788  | 3540    | 手机游戏  | BV1Kz4y1S7pg |\n| 2022/10/26 | [本音<em class=\"keyword\">星瞳</em>来咯](http://www.bilibili.com/video/av774460230)                                                                                             | 虎来八荒        | 1:53    | 101456  | 1183    | 动画综合  | BV1i14y1575R |\n| 2025/08/28 | [七海雪糕阿梓合唱了<em class=\"keyword\">星瞳</em>的猫舞喵呜，让瞳姐也有参与感！【七海】](http://www.bilibili.com/video/av115106600061550)                 | 欧气满满的小星星    | 3:26    | 1209    | 38      | 动画综合  | BV1NGhmzzEa3 |",
#       "name": "search_video",
#       "id": "0cecc81b-504a-4cdd-9f88-9d72f009068a",
#       "tool_call_id": "call_d2omk9c2c3mfrjfd4bj0"
#     },
#     {
#       "type": "AIMessage",
#       "content": "星瞳最新的视频是：\n\n- **标题**: 【柚恩】真压抑了！杭州癔症姐看<em class=\"keyword\">星瞳</em>《大展宏图》整个人都软了：<em class=\"keyword\">星瞳</em>嘴皮子好快，我这个铁血小星星好喜欢�\n- **发布日期**: 2025/08/24\n- **UP主**: 珈蓝梦\n- **时长**: 3:29\n- **播放量**: 11450\n- **点赞数**: 237\n- **类别**: 动画综合\n- **BV号**: BV1TxeHzyEyL\n\n你可以点击链接观看：[视频链接](http://www.bilibili.com/video/av115083883580074)",
#       "additional_kwargs": {
#         "refusal": null
#       },
#       "response_metadata": {
#         "token_usage": {
#           "completion_tokens": 174,
#           "prompt_tokens": 2882,
#           "total_tokens": 3056,
#           "completion_tokens_details": null,
#           "prompt_tokens_details": null
#         },
#         "model_name": "hunyuan-standard-256K",
#         "system_fingerprint": null,
#         "id": "16e53027b5f14e13bb352fdac1460c9a",
#         "service_tier": null,
#         "finish_reason": "stop",
#         "logprobs": null
#       },
#       "id": "run--5d1edcc3-d284-4f9e-ac37-b95a751c32c1-0",
#       "usage_metadata": {
#         "input_tokens": 2882,
#         "output_tokens": 174,
#         "total_tokens": 3056,
#         "input_token_details": {},
#         "output_token_details": {}
#       }
#     }
#   ]
# }

asyncio.run(main())
