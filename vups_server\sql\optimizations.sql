-- Database optimization script for VUPS server
-- This script adds strategic indexes and optimizations based on query patterns

-- ============================================================================
-- CURRENT_STAT_TABLE OPTIMIZATIONS
-- ============================================================================

-- Primary index for UID-based queries (most common pattern)
CREATE INDEX IF NOT EXISTS idx_current_stat_uid_datetime 
ON current_stat_table (uid, datetime DESC);

-- Index for time-range queries
CREATE INDEX IF NOT EXISTS idx_current_stat_datetime 
ON current_stat_table (datetime DESC);

-- Composite index for follower tracking queries
CREATE INDEX IF NOT EXISTS idx_current_stat_uid_follower 
ON current_stat_table (uid, datetime DESC) 
WHERE follower_num IS NOT NULL;

-- Composite index for dahanghai tracking queries
CREATE INDEX IF NOT EXISTS idx_current_stat_uid_dahanghai 
ON current_stat_table (uid, datetime DESC) 
WHERE dahanghai_num IS NOT NULL;

-- Index for date-based aggregations
CREATE INDEX IF NOT EXISTS idx_current_stat_date 
ON current_stat_table (DATE(datetime), uid);

-- ============================================================================
-- VIDEOS_TABLE OPTIMIZATIONS
-- ============================================================================

-- Primary index for UID-based video queries
CREATE INDEX IF NOT EXISTS idx_videos_uid_datetime 
ON videos_table (uid, datetime DESC);

-- Index for BVID lookups (unique video identification)
CREATE INDEX IF NOT EXISTS idx_videos_bvid 
ON videos_table (bvid);

-- Index for heat-based top video queries
CREATE INDEX IF NOT EXISTS idx_videos_uid_heat 
ON videos_table (uid, heat DESC, datetime DESC);

-- Index for recent video queries with time filter
CREATE INDEX IF NOT EXISTS idx_videos_uid_recent 
ON videos_table (uid, datetime DESC) 
WHERE datetime >= NOW() - INTERVAL '90 days';

-- Index for video AI conclusion queries
CREATE INDEX IF NOT EXISTS idx_videos_bvid_ai_conclusion 
ON videos_table (bvid) 
WHERE video_ai_conclusion IS NOT NULL;

-- ============================================================================
-- DYNAMICS_TABLE OPTIMIZATIONS
-- ============================================================================

-- Primary index for UID-based dynamics queries
CREATE INDEX IF NOT EXISTS idx_dynamics_uid_datetime 
ON dynamics_table (uid, datetime DESC);

-- Index for dynamic ID lookups
CREATE INDEX IF NOT EXISTS idx_dynamics_dynamic_id 
ON dynamics_table (dynamic_id);

-- Index for heat-based top dynamics queries
CREATE INDEX IF NOT EXISTS idx_dynamics_uid_heat 
ON dynamics_table (uid, heat DESC, datetime DESC);

-- Index for content-based searches (partial index for non-null content)
CREATE INDEX IF NOT EXISTS idx_dynamics_uid_content 
ON dynamics_table (uid, datetime DESC) 
WHERE dynamic_content IS NOT NULL;

-- ============================================================================
-- USER_INFO_TABLE OPTIMIZATIONS
-- ============================================================================

-- Primary index for UID lookups (should already exist as primary key)
CREATE INDEX IF NOT EXISTS idx_user_info_uid 
ON user_info_table (uid);

-- Index for name-based searches
CREATE INDEX IF NOT EXISTS idx_user_info_name 
ON user_info_table (name);

-- ============================================================================
-- COMMENT TABLES OPTIMIZATIONS (TEMPLATE)
-- ============================================================================

-- Note: These indexes should be created for each user's comment tables
-- Template for video comment tables: video_comment_{uid}

-- CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_time 
-- ON video_comment_{uid} (comment_time DESC);

-- CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_likes 
-- ON video_comment_{uid} (like_num DESC, comment_time DESC);

-- CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_bvid 
-- ON video_comment_{uid} (bvid, comment_time DESC);

-- CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_user 
-- ON video_comment_{uid} (comment_user_uid, comment_time DESC);

-- Template for dynamics comment tables: dynamics_comment_{uid}

-- CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_time 
-- ON dynamics_comment_{uid} (comment_time DESC);

-- CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_likes 
-- ON dynamics_comment_{uid} (like_num DESC, comment_time DESC);

-- CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_dynamic 
-- ON dynamics_comment_{uid} (dynamic_id, comment_time DESC);

-- CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_user 
-- ON dynamics_comment_{uid} (comment_user_uid, comment_time DESC);

-- ============================================================================
-- AI_GEN_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based AI data queries
CREATE INDEX IF NOT EXISTS idx_ai_gen_uid_datetime 
ON ai_gen_table (uid, datetime DESC);

-- Partial indexes for specific AI data types
CREATE INDEX IF NOT EXISTS idx_ai_gen_uid_relationship 
ON ai_gen_table (uid, datetime DESC) 
WHERE relationship IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_ai_gen_uid_tieba_summary 
ON ai_gen_table (uid, datetime DESC) 
WHERE tieba_summary IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_ai_gen_uid_rise_reason 
ON ai_gen_table (uid, datetime DESC) 
WHERE rise_reason IS NOT NULL;

-- ============================================================================
-- TIEBA_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based tieba queries
CREATE INDEX IF NOT EXISTS idx_tieba_uid_datetime 
ON tieba_table (uid, datetime DESC);

-- ============================================================================
-- TIEBA_THREADS_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based thread queries
CREATE INDEX IF NOT EXISTS idx_tieba_threads_uid_time 
ON tieba_threads_table (uid, thread_time DESC);

-- Index for thread ID lookups
CREATE INDEX IF NOT EXISTS idx_tieba_threads_thread_id 
ON tieba_threads_table (thread_id);

-- Index for forum-based queries
CREATE INDEX IF NOT EXISTS idx_tieba_threads_forum 
ON tieba_threads_table (forum_name, thread_time DESC);

-- ============================================================================
-- FANS_MEDAL_RANK_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based fans medal queries
CREATE INDEX IF NOT EXISTS idx_fans_medal_uid_datetime 
ON fans_medal_rank_table (uid, datetime DESC);

-- ============================================================================
-- FOLLOWERS_LIST_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based followers queries
CREATE INDEX IF NOT EXISTS idx_followers_list_uid_datetime 
ON followers_list_table (uid, datetime DESC);

-- ============================================================================
-- DAHANGHAI_LIST_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based dahanghai queries
CREATE INDEX IF NOT EXISTS idx_dahanghai_list_uid_datetime 
ON dahanghai_list_table (uid, datetime DESC);

-- ============================================================================
-- VIDEO_DAY_DATA_TABLE OPTIMIZATIONS
-- ============================================================================

-- Composite index for video day data queries
CREATE INDEX IF NOT EXISTS idx_video_day_data_uid_bvid_datetime 
ON video_day_data_table (uid, bvid, datetime DESC);

-- Index for BVID-based queries
CREATE INDEX IF NOT EXISTS idx_video_day_data_bvid_datetime 
ON video_day_data_table (bvid, datetime DESC);

-- ============================================================================
-- COMMENTS_SENTIMENT_TABLE OPTIMIZATIONS
-- ============================================================================

-- Index for UID-based sentiment queries
CREATE INDEX IF NOT EXISTS idx_comments_sentiment_uid_datetime 
ON comments_sentiment_table (uid, datetime DESC);

-- ============================================================================
-- PERFORMANCE TUNING SETTINGS
-- ============================================================================

-- Increase work_mem for complex queries (adjust based on available RAM)
-- SET work_mem = '256MB';

-- Increase shared_buffers for better caching (adjust based on available RAM)
-- SET shared_buffers = '1GB';

-- Enable parallel query execution for large datasets
-- SET max_parallel_workers_per_gather = 4;

-- Optimize random page cost for SSD storage
-- SET random_page_cost = 1.1;

-- ============================================================================
-- MAINTENANCE COMMANDS
-- ============================================================================

-- Update table statistics for better query planning
-- ANALYZE current_stat_table;
-- ANALYZE videos_table;
-- ANALYZE dynamics_table;
-- ANALYZE user_info_table;
-- ANALYZE ai_gen_table;
-- ANALYZE tieba_table;
-- ANALYZE tieba_threads_table;

-- Vacuum tables to reclaim space and update statistics
-- VACUUM ANALYZE current_stat_table;
-- VACUUM ANALYZE videos_table;
-- VACUUM ANALYZE dynamics_table;

-- ============================================================================
-- LIVE STREAMING DATA OPTIMIZATIONS
-- ============================================================================

-- Gift table indexes for room and time-based queries
CREATE INDEX IF NOT EXISTS idx_gift_table_room_timestamp
ON gift_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_gift_table_room_datetime
ON gift_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_gift_table_live_id
ON gift_table (live_id);

-- SuperChat table indexes
CREATE INDEX IF NOT EXISTS idx_super_chat_table_room_start_timestamp
ON super_chat_table (room_id, start_timestamp);

CREATE INDEX IF NOT EXISTS idx_super_chat_table_room_datetime
ON super_chat_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_super_chat_table_live_id
ON super_chat_table (live_id);

-- Buy guard table indexes
CREATE INDEX IF NOT EXISTS idx_buy_guard_table_room_timestamp
ON buy_guard_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_buy_guard_table_room_datetime
ON buy_guard_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_buy_guard_table_live_id
ON buy_guard_table (live_id);

-- Online rank count minute table indexes
CREATE INDEX IF NOT EXISTS idx_online_rank_count_minute_room_timestamp
ON online_rank_count_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_online_rank_count_minute_room_datetime
ON online_rank_count_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_online_rank_count_minute_live_id
ON online_rank_count_minute_table (live_id);

-- Interact word count minute table indexes
CREATE INDEX IF NOT EXISTS idx_interact_word_count_minute_room_timestamp
ON interact_word_count_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_interact_word_count_minute_room_datetime
ON interact_word_count_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_interact_word_count_minute_live_id
ON interact_word_count_minute_table (live_id);

-- Enter room count minute table indexes
CREATE INDEX IF NOT EXISTS idx_enter_room_count_minute_room_timestamp
ON enter_room_count_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_enter_room_count_minute_room_datetime
ON enter_room_count_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_enter_room_count_minute_live_id
ON enter_room_count_minute_table (live_id);

-- Danmu count minute table indexes
CREATE INDEX IF NOT EXISTS idx_danmu_count_minute_room_timestamp
ON danmu_count_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_danmu_count_minute_room_datetime
ON danmu_count_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_danmu_count_minute_live_id
ON danmu_count_minute_table (live_id);

-- Active watcher count minute table indexes
CREATE INDEX IF NOT EXISTS idx_active_watcher_count_minute_room_timestamp
ON active_watcher_count_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_active_watcher_count_minute_room_datetime
ON active_watcher_count_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_active_watcher_count_minute_live_id
ON active_watcher_count_minute_table (live_id);

-- Income minute table indexes
CREATE INDEX IF NOT EXISTS idx_income_minute_room_timestamp
ON income_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_income_minute_room_datetime
ON income_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_income_minute_live_id
ON income_minute_table (live_id);

-- Live status minute table indexes
CREATE INDEX IF NOT EXISTS idx_live_status_minute_room_timestamp
ON live_status_minute_table (room_id, timestamp);

CREATE INDEX IF NOT EXISTS idx_live_status_minute_room_datetime
ON live_status_minute_table (room_id, datetime);

CREATE INDEX IF NOT EXISTS idx_live_status_minute_live_id
ON live_status_minute_table (live_id);

-- Optimized index for latest live status queries
CREATE INDEX IF NOT EXISTS idx_live_status_minute_room_timestamp_desc
ON live_status_minute_table (room_id, timestamp DESC);

-- Live session table indexes
CREATE INDEX IF NOT EXISTS idx_live_session_room_datetime
ON live_session_table (room_id, datetime DESC);

CREATE INDEX IF NOT EXISTS idx_live_session_live_id
ON live_session_table (live_id);

CREATE INDEX IF NOT EXISTS idx_live_session_room_start_time
ON live_session_table (room_id, start_time_str);

-- Template indexes for dynamic danmu tables (to be created per room)
-- These should be created when danmu_table_{room_id} tables are created:
-- CREATE INDEX IF NOT EXISTS idx_danmu_table_{room_id}_timestamp
-- ON danmu_table_{room_id} (timestamp);
-- CREATE INDEX IF NOT EXISTS idx_danmu_table_{room_id}_datetime
-- ON danmu_table_{room_id} (datetime);
-- CREATE INDEX IF NOT EXISTS idx_danmu_table_{room_id}_live_id
-- ON danmu_table_{room_id} (live_id);

-- ============================================================================
-- MONITORING QUERIES
-- ============================================================================

-- Query to check index usage
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes
-- ORDER BY idx_scan DESC;

-- Query to find slow queries
-- SELECT query, mean_time, calls, total_time
-- FROM pg_stat_statements
-- ORDER BY mean_time DESC
-- LIMIT 10;

-- Query to check table sizes
-- SELECT schemaname, tablename,
--        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
-- FROM pg_tables
-- WHERE schemaname = 'public'
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
