import re
import json


def fix_json_format(raw_str):
    """
    Fix malformed JSON-like strings to proper JSON format.
    Handles mixed language text, apostrophes, and proper escaping.

    Args:
        raw_str: Raw string that may contain Python-like syntax or malformed JSON

    Returns:
        Fixed JSON string that should be parseable by json.loads()
    """
    import ast

    # Try to use ast.literal_eval first for Python-like syntax
    try:
        # This handles most Python literal cases safely
        parsed = ast.literal_eval(raw_str)
        return json.dumps(parsed, ensure_ascii=False)
    except (ValueError, SyntaxError):
        # Fall back to regex-based fixing
        pass

    # Step 1: Convert Python literals to JSON equivalents
    fixed = re.sub(r'\bTrue\b', 'true', raw_str)
    fixed = re.sub(r'\bFalse\b', 'false', fixed)
    fixed = re.sub(r'\bNone\b', 'null', fixed)

    # Step 2: Fix unquoted keys (word followed by colon)
    fixed = re.sub(r'(\w+)\s*:', r'"\1":', fixed)

    # Step 3: Handle string values using a simpler, more reliable approach
    # Replace single quotes with double quotes, but preserve apostrophes
    def replace_quotes(match):
        content = match.group(1)
        # Escape any existing double quotes
        content = content.replace('"', '\\"')
        return f'"{content}"'

    # Find single-quoted strings and convert them
    # This pattern matches 'content' where content doesn't contain unescaped single quotes
    fixed = re.sub(r"'([^']*(?:\\'[^']*)*)'", replace_quotes, fixed)

    # Step 4: Remove trailing commas before closing brackets/braces
    fixed = re.sub(r',\s*([}\]])', r'\1', fixed)

    return fixed


def safe_json_loads(json_str):
    """
    Safely load JSON with fallback to fix_json_format if parsing fails.

    Args:
        json_str: JSON string to parse

    Returns:
        Parsed JSON object

    Raises:
        json.JSONDecodeError: If JSON cannot be parsed even after fixing
    """
    try:
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            fixed_str = fix_json_format(json_str)
            return json.loads(fixed_str)
        except json.JSONDecodeError as e:
            # If still failing, provide more context in the error
            raise json.JSONDecodeError(
                f"Failed to parse JSON even after fixing. Original error: {e}",
                json_str,
                e.pos
            )
