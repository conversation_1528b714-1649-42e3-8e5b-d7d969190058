#!/usr/bin/env python3
"""
User Data Query Wrapper Script
Wraps the existing user data query functionality for Go backend integration
"""

import sys
import json
import asyncio
from pathlib import Path

# Add the parent directory to Python path to import vups modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from vups_server.query.query_vup_user_data import (
        query_current_stat_by_mid,
        query_user_info_by_mid,
        query_user_dynamics_by_mid,
        query_all_video_list_by_mid,
        query_now_user_follower_num_by_mid,
        query_now_user_dahanghai_num_by_mid,
        calculate_follower_rate_by_mid,
        calculate_dahanghai_rate_by_mid,
        query_recent_info,
        query_top_n_comments,
        query_top_n_videos,
        query_top_n_dynamics
    )
    from vups.logger import logger
except ImportError as e:
    print(json.dumps({
        "success": False,
        "error": f"Failed to import vups modules: {str(e)}",
        "data": None
    }))
    sys.exit(1)


async def query_user_stats(uid):
    """Query user statistics"""
    try:
        current_stat = await query_current_stat_by_mid(uid)
        follower_count = await query_now_user_follower_num_by_mid(uid)
        dahanghai_count = await query_now_user_dahanghai_num_by_mid(uid)
        follower_rate = await calculate_follower_rate_by_mid(uid)
        dahanghai_rate = await calculate_dahanghai_rate_by_mid(uid)
        
        return {
            "current_stat": dict(current_stat) if current_stat else None,
            "follower_count": follower_count,
            "dahanghai_count": dahanghai_count,
            "follower_growth_rate": follower_rate,
            "dahanghai_growth_rate": dahanghai_rate,
            "type": "user_stats"
        }
    except Exception as e:
        raise Exception(f"Failed to query user stats: {str(e)}")


async def query_user_content(uid):
    """Query user content (videos and dynamics)"""
    try:
        videos = await query_all_video_list_by_mid(uid)
        dynamics = await query_user_dynamics_by_mid(uid)
        
        return {
            "videos": videos if videos else [],
            "dynamics": dynamics if dynamics else [],
            "type": "user_content"
        }
    except Exception as e:
        raise Exception(f"Failed to query user content: {str(e)}")


async def query_user_analytics(uid, limit=10):
    """Query user analytics data"""
    try:
        top_comments = await query_top_n_comments(uid, limit)
        top_videos = await query_top_n_videos(uid, limit)
        top_dynamics = await query_top_n_dynamics(uid, limit)
        
        return {
            "top_comments": top_comments if top_comments else [],
            "top_videos": top_videos if top_videos else [],
            "top_dynamics": top_dynamics if top_dynamics else [],
            "type": "user_analytics"
        }
    except Exception as e:
        raise Exception(f"Failed to query user analytics: {str(e)}")


async def query_user_info(uid):
    """Query comprehensive user information"""
    try:
        user_info = await query_user_info_by_mid(uid)
        recent_info = await query_recent_info(uid)
        
        return {
            "user_info": dict(user_info) if user_info else None,
            "recent_info": recent_info if recent_info else None,
            "type": "user_info"
        }
    except Exception as e:
        raise Exception(f"Failed to query user info: {str(e)}")


async def query_user_period_stats(uid, start_time, end_time):
    """Query user statistics for a specific period"""
    try:
        from vups_server.query.query_vup_user_data import query_peroid_user_all_stat_by_uid_and_time
        
        period_stats = await query_peroid_user_all_stat_by_uid_and_time(uid, start_time, end_time)
        
        return {
            "period_stats": period_stats if period_stats else [],
            "start_time": start_time,
            "end_time": end_time,
            "type": "user_period_stats"
        }
    except Exception as e:
        raise Exception(f"Failed to query period stats: {str(e)}")


async def main():
    """Main function to execute user data queries"""
    if len(sys.argv) < 5:
        print(json.dumps({
            "success": False,
            "error": "Missing required arguments: uid, start_time, end_time, data_type",
            "data": None
        }))
        sys.exit(1)
    
    uid = sys.argv[1]
    start_time = sys.argv[2]
    end_time = sys.argv[3]
    data_type = sys.argv[4]
    
    try:
        result = None
        
        if data_type == "stats":
            result = await query_user_stats(uid)
        elif data_type == "content":
            result = await query_user_content(uid)
        elif data_type == "analytics":
            result = await query_user_analytics(uid)
        elif data_type == "info":
            result = await query_user_info(uid)
        elif data_type == "period_stats":
            result = await query_user_period_stats(uid, start_time, end_time)
        else:
            raise Exception(f"Unsupported data type: {data_type}")
        
        # Return successful result
        print(json.dumps({
            "success": True,
            "data": result,
            "error": None
        }, ensure_ascii=False, default=str))
        
    except Exception as e:
        logger.error(f"User data query error: {str(e)}")
        print(json.dumps({
            "success": False,
            "error": str(e),
            "data": None
        }))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
