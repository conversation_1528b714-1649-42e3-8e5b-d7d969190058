"""
Example usage of creator query modules.
Demonstrates how to retrieve historical data collections for different time periods.
"""

import asyncio
from datetime import datetime, timedelta
from vups_server.query.creator_query import (
    creator_overview_service,
    creator_weekly_service,
    creator_video_service,
    creator_analytics_service
)


async def example_daily_data_retrieval():
    """Example: Retrieve data for a specific past day."""
    print("=== Daily Data Retrieval Example ===")
    
    uid = "123456"  # Replace with actual UID
    target_date = "2024-01-15"  # Replace with actual date
    
    # Get daily overview data
    daily_overview = await creator_overview_service.get_daily_overview_by_uid_and_date(
        uid=uid,
        date=target_date
    )
    
    if daily_overview:
        print(f"Daily overview for {target_date}:")
        print(f"  Play count: {daily_overview['play']}")
        print(f"  Fan count: {daily_overview['fan']}")
        print(f"  Like count: {daily_overview['like_count']}")
    else:
        print(f"No daily data found for {target_date}")
    
    # Get comprehensive day data
    comprehensive_day = await creator_analytics_service.get_comprehensive_day_data(
        uid=uid,
        date=target_date
    )
    
    print(f"Comprehensive day data availability:")
    print(f"  Has overview: {comprehensive_day['data_availability']['has_overview']}")
    print(f"  Video count: {comprehensive_day['data_availability']['video_count']}")
    print(f"  Has electric data: {comprehensive_day['data_availability']['has_electric_data']}")


async def example_weekly_data_retrieval():
    """Example: Retrieve data for a specific past week."""
    print("\n=== Weekly Data Retrieval Example ===")
    
    uid = "123456"  # Replace with actual UID
    week_start = "2024-01-08"  # Replace with actual week start date
    
    # Get weekly overview data
    weekly_overview = await creator_weekly_service.get_weekly_overview_data(
        uid=uid,
        date=week_start
    )
    
    if weekly_overview:
        print(f"Weekly overview for week starting {week_start}:")
        print(f"  Play count amount: {weekly_overview.get('play_cnt_amount', 'N/A')}")
        print(f"  Interact rate amount: {weekly_overview.get('interact_rate_amount', 'N/A')}")
    else:
        print(f"No weekly overview data found for week starting {week_start}")
    
    # Get comprehensive week data
    comprehensive_week = await creator_analytics_service.get_comprehensive_week_data(
        uid=uid,
        week_start_date=week_start
    )
    
    print(f"Comprehensive week data availability:")
    print(f"  Has weekly overview: {comprehensive_week['data_availability']['has_weekly_overview']}")
    print(f"  Has play analyze: {comprehensive_week['data_availability']['has_play_analyze']}")
    print(f"  Daily data count: {comprehensive_week['data_availability']['daily_data_count']}")


async def example_monthly_data_retrieval():
    """Example: Retrieve data for a specific past month."""
    print("\n=== Monthly Data Retrieval Example ===")
    
    uid = "123456"  # Replace with actual UID
    month_start = "2024-01-01"  # Replace with actual month start date
    
    # Get comprehensive month data
    comprehensive_month = await creator_analytics_service.get_comprehensive_month_data(
        uid=uid,
        month_start_date=month_start
    )
    
    print(f"Comprehensive month data for {month_start}:")
    print(f"  Daily data count: {comprehensive_month['data_availability']['daily_data_count']}")
    print(f"  Video compare count: {comprehensive_month['data_availability']['video_compare_count']}")
    print(f"  Weekly metrics count: {comprehensive_month['data_availability']['weekly_metrics_count']}")
    
    # Display aggregated statistics
    if comprehensive_month['aggregated_statistics']:
        stats = comprehensive_month['aggregated_statistics']
        print(f"  Aggregated statistics:")
        print(f"    Period days: {stats['period_days']}")
        print(f"    Total play: {stats['totals']['total_play']}")
        print(f"    Fan growth: {stats['growth']['fan_growth']}")
        print(f"    Fan growth rate: {stats['growth']['fan_growth_rate']}%")


async def example_configurable_range_retrieval():
    """Example: Retrieve data for configurable date ranges."""
    print("\n=== Configurable Date Range Example ===")
    
    uid = "123456"  # Replace with actual UID
    start_date = "2024-01-01"
    end_date = "2024-01-31"
    
    # Get data for custom range with all data types
    range_data = await creator_analytics_service.get_data_by_configurable_range(
        uid=uid,
        start_date=start_date,
        end_date=end_date,
        include_video_data=True,
        include_weekly_data=True
    )
    
    print(f"Configurable range data ({start_date} to {end_date}):")
    print(f"  Daily data count: {range_data['data_availability']['daily_data_count']}")
    print(f"  Video data count: {range_data['data_availability'].get('video_compare_count', 0)}")
    print(f"  Weekly data count: {range_data['data_availability'].get('weekly_metrics_count', 0)}")
    
    # Get overview data for specific range
    overview_range = await creator_overview_service.get_overview_data_by_date_range(
        uid=uid,
        start_date=start_date,
        end_date=end_date
    )
    
    print(f"Overview range data: {len(overview_range)} records")


async def example_recent_data_retrieval():
    """Example: Retrieve recent data using relative time periods."""
    print("\n=== Recent Data Retrieval Example ===")
    
    uid = "123456"  # Replace with actual UID
    
    # Get data for yesterday
    yesterday_data = await creator_overview_service.get_past_day_data(uid, days_ago=1)
    if yesterday_data:
        print(f"Yesterday's data: Play={yesterday_data['play']}, Fan={yesterday_data['fan']}")
    else:
        print("No data found for yesterday")
    
    # Get data for last week
    last_week_data = await creator_overview_service.get_past_week_overview_summary(uid, weeks_ago=1)
    print(f"Last week's daily data: {len(last_week_data)} records")
    
    # Get data for last month
    last_month_data = await creator_overview_service.get_past_month_overview_summary(uid, months_ago=1)
    print(f"Last month's daily data: {len(last_month_data)} records")
    
    # Get recent video performance
    recent_videos = await creator_video_service.get_recent_video_performance(uid, days=7)
    print(f"Recent video performance (7 days): {len(recent_videos)} videos")


async def example_video_data_retrieval():
    """Example: Retrieve video-related data."""
    print("\n=== Video Data Retrieval Example ===")
    
    uid = "123456"  # Replace with actual UID
    
    # Get video archive data
    video_archives = await creator_video_service.get_video_archive_data_by_uid(uid, limit=10)
    print(f"Video archives: {len(video_archives)} records")
    
    # Get latest electric number data
    latest_elec = await creator_video_service.get_latest_video_elec_num(uid)
    if latest_elec:
        print(f"Latest electric numbers:")
        print(f"  All total: {latest_elec['all_total']}")
        print(f"  Month total: {latest_elec['month_total']}")
    else:
        print("No electric number data found")
    
    # Get past month video data
    past_month_videos = await creator_video_service.get_past_month_video_data(uid, months_ago=1)
    print(f"Past month video data: {len(past_month_videos)} records")


async def main():
    """Run all examples."""
    print("Creator Query Modules Usage Examples")
    print("=" * 50)
    
    try:
        await example_daily_data_retrieval()
        await example_weekly_data_retrieval()
        await example_monthly_data_retrieval()
        await example_configurable_range_retrieval()
        await example_recent_data_retrieval()
        await example_video_data_retrieval()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Note: Make sure to replace the example UID with a real UID from your database")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
