from typing import List, <PERSON>ple

from vups_server.query.live_query import (
    live_danmu_service,
    live_gift_service,
    live_statistics_service,
    live_status_service,
    live_analytics_service
)


# ============================================================================
# DANMU QUERIES - Backward compatibility functions
# ============================================================================

async def query_danmu_by_room_and_timespan(room_id, start_ts, end_ts) -> Tuple[List, int]:
    """
    DEPRECATED: Use LiveDanmuService.query_by_room_and_timespan() instead.

    查询指定直播间在时间范围内的弹幕数据
    """
    return await live_danmu_service.query_by_room_and_timespan(room_id, start_ts, end_ts)


async def query_danmu_by_room_and_datetime(room_id, start_datetime, end_datetime) -> Tuple[List, int]:
    """
    DEPRECATED: Use LiveDanmuService.query_by_room_and_datetime() instead.

    查询指定直播间在指定日期范围内的弹幕数据
    """
    return await live_danmu_service.query_by_room_and_datetime(room_id, start_datetime, end_datetime)


# ============================================================================
# SUPERCHAT QUERIES - Backward compatibility functions
# ============================================================================

async def query_superchat_by_room_and_timespan(room_id, start_ts, end_ts) -> Tuple[List, int]:
    """
    DEPRECATED: Use LiveGiftService.query_superchat_by_room_and_timespan() instead.

    查询指定直播间在时间范围内的SC数据
    """
    return await live_gift_service.query_superchat_by_room_and_timespan(room_id, start_ts, end_ts)


async def query_superchat_by_room_and_datetime(room_id, start_datetime, end_datetime) -> Tuple[List, int]:
    """
    DEPRECATED: Use LiveGiftService.query_superchat_by_room_and_datetime() instead.

    查询指定直播间在时间范围内的SC数据
    """
    return await live_gift_service.query_superchat_by_room_and_datetime(room_id, start_datetime, end_datetime)


# ============================================================================
# GIFT QUERIES - Backward compatibility functions
# ============================================================================

async def query_gift_by_room_and_timespan(room_id, start_ts, end_ts) -> Tuple[List, int]:
    """
    DEPRECATED: Use LiveGiftService.query_gift_by_room_and_timespan() instead.

    查询指定直播间在时间范围内的礼物数据
    """
    return await live_gift_service.query_gift_by_room_and_timespan(room_id, start_ts, end_ts)


async def query_gift_by_room_and_datetime(room_id, start_datetime, end_datetime) -> Tuple[List, int]:
    """
    DEPRECATED: Use LiveGiftService.query_gift_by_room_and_datetime() instead.

    查询指定直播间在时间范围内的礼物数据
    """
    return await live_gift_service.query_gift_by_room_and_datetime(room_id, start_datetime, end_datetime)


# ============================================================================
# COUNT/STATISTICS QUERIES - Backward compatibility functions
# ============================================================================

async def query_enter_room_count_by_room_and_time(room_id, ts) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_enter_room_count_by_room_and_time() instead.

    查询指定直播间在指定时间点的进入房间次数
    """
    return await live_statistics_service.query_enter_room_count_by_room_and_time(room_id, ts)


async def query_interact_word_count_by_room_and_time(room_id, ts) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_interact_word_count_by_room_and_time() instead.

    查询指定直播间在指定时间点的互动次数
    """
    return await live_statistics_service.query_interact_word_count_by_room_and_time(room_id, ts)


async def query_active_watcher_count_by_room_and_time(room_id, ts) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_active_watcher_count_by_room_and_time() instead.

    查询指定直播间在指定时间点的活跃观众数量
    """
    return await live_statistics_service.query_active_watcher_count_by_room_and_time(room_id, ts)


async def query_active_watcher_count_by_room_and_datetime(room_id, start_datetime, end_datetime) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_active_watcher_count_by_room_and_datetime() instead.

    查询指定直播间在指定时间段的活跃观众数量累计
    """
    return await live_statistics_service.query_active_watcher_count_by_room_and_datetime(room_id, start_datetime, end_datetime)


async def query_interact_word_count_by_room_and_datetime(room_id, start_datetime, end_datetime) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_interact_word_count_by_room_and_datetime() instead.

    查询指定直播间在指定时间段的互动次数累计次数
    """
    return await live_statistics_service.query_interact_word_count_by_room_and_datetime(room_id, start_datetime, end_datetime)


async def query_online_rank_count_by_room_and_time(room_id, ts) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_online_rank_count_by_room_and_time() instead.

    查询指定直播间在指定高能榜的在线人数
    """
    return await live_statistics_service.query_online_rank_count_by_room_and_time(room_id, ts)


async def query_max_online_rank_count_by_room_and_datetime(room_id, start_datetime, end_datetime) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_max_online_rank_count_by_room_and_datetime() instead.

    查询指定直播间在指定时间的最大在线人数
    """
    return await live_statistics_service.query_max_online_rank_count_by_room_and_datetime(room_id, start_datetime, end_datetime)


async def query_average_online_rank_count_by_room_and_datetime(room_id, start_datetime, end_datetime) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_average_online_rank_count_by_room_and_datetime() instead.

    查询指定直播间在指定时间段的平均高能榜人数
    """
    return await live_statistics_service.query_average_online_rank_count_by_room_and_datetime(room_id, start_datetime, end_datetime)


async def query_average_enter_room_count_by_room_and_datetime(room_id, start_datetime, end_datetime) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_average_enter_room_count_by_room_and_datetime() instead.

    查询指定直播间在指定时间段的平均进房人数
    """
    return await live_statistics_service.query_average_enter_room_count_by_room_and_datetime(room_id, start_datetime, end_datetime)


async def query_total_enter_room_count_by_room_and_datetime(room_id, start_datetime, end_datetime) -> int:
    """
    DEPRECATED: Use LiveStatisticsService.query_total_enter_room_count_by_room_and_datetime() instead.

    查询指定直播间在指定时间段的总进房人数
    """
    return await live_statistics_service.query_total_enter_room_count_by_room_and_datetime(room_id, start_datetime, end_datetime)


# ============================================================================
# LIVE STATUS QUERIES - Backward compatibility functions
# ============================================================================

async def query_live_status_by_room_and_time(room_id, ts) -> int:
    """
    DEPRECATED: Use LiveStatusService.query_live_status_by_room_and_time() instead.

    查询指定直播间在指定时间点的直播状态
    """
    return await live_status_service.query_live_status_by_room_and_time(room_id, ts)


async def query_now_live_info_by_room(room_id):
    """
    DEPRECATED: Use LiveStatusService.query_now_live_info_by_room() instead.

    查询指定直播间当前直播状态
    """
    return await live_status_service.query_now_live_info_by_room(room_id)


async def query_live_start_end_time_by_live_date(room_id, live_date_str, date_is_start_live_date=False):
    """
    DEPRECATED: Use LiveStatusService.query_live_start_end_time_by_live_date() instead.

    根据房间ID和日期查询直播开始和结束时间
    """
    return await live_status_service.query_live_start_end_time_by_live_date(room_id, live_date_str, date_is_start_live_date)


async def query_live_start_time_by_end_time(room_id, end_time_str):
    """
    DEPRECATED: Use LiveStatusService.query_live_start_time_by_end_time() instead.

    根据直播结束时间，查询当天直播开始时间
    """
    return await live_status_service.query_live_start_time_by_end_time(room_id, end_time_str)


# ============================================================================
# PAYMENT QUERIES - Backward compatibility functions
# ============================================================================

async def query_pay_count_by_room_and_live_date(room_id, live_date):
    """
    DEPRECATED: Use LiveGiftService.query_pay_count_by_room_and_live_date() instead.

    根据房间ID和直播日期查询付费次数和收入
    """
    return await live_gift_service.query_pay_count_by_room_and_live_date(room_id, live_date)


async def query_pay_count_by_room_and_live_start_end_time(room_id, in_start_time, in_end_time):
    """
    DEPRECATED: Use LiveGiftService.query_pay_count_by_room_and_live_start_end_time() instead.

    根据房间ID和时间范围查询付费次数和收入
    """
    return await live_gift_service.query_pay_count_by_room_and_live_start_end_time(room_id, in_start_time, in_end_time)


# ============================================================================
# ANALYTICS QUERIES - Backward compatibility functions
# ============================================================================

async def query_whole_live_info_with_live_id(live_id):
    """
    DEPRECATED: Use LiveAnalyticsService.query_whole_live_info_with_live_id() instead.

    通过 live_id 异步查询完整的直播信息
    """
    return await live_analytics_service.query_whole_live_info_with_live_id(live_id)


async def query_minutes_live_info_with_live_id(live_id, interval=10):
    """
    DEPRECATED: Use LiveAnalyticsService.query_minutes_live_info_with_live_id() instead.

    查询分钟级别的直播信息
    """
    return await live_analytics_service.query_minutes_live_info_with_live_id(live_id, interval)


async def query_live_info_with_room_id(room_id):
    """
    DEPRECATED: Use LiveAnalyticsService.query_live_info_with_room_id() instead.

    异步查询给定房间ID的所有直播信息，并包含聚合统计数据。
    """
    return await live_analytics_service.query_live_info_with_room_id(room_id)
