from importlib.resources import read_text
import os
import re
import json

from langchain.schema import HumanMessage, SystemMessage

from vups.base.action.abstract_action import Action
from vups.algos.prompts import load_prompt
import vups.utils as U
from vups.logger import logger
from bilibili_api import video
from vups.algos.tools.video_dump import video_source_downloader

class VideoAnalyse(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
        output_dir: str = "output",
    ):
        super().__init__(llm_name, "video_analyse")
        self.token_max = 5000

        self.output_dir = output_dir
        os.makedirs(self.output_dir + "/video_analyse", exist_ok=True)

    #     self.char = char
    #     self.set_char(self.char)

    # def set_char(self, char):
    #     self.char = char
    #     self.char_zh = U.get_zh_role_name(self.char)

    def render_rel_human_message(self, transcript_text: str):
        prompt = load_prompt("video_analyse")
        prompt = prompt.format(transcript_text=transcript_text)
        return HumanMessage(content=prompt)

    def render_rel_system_message(self):
        prompt = "你是一个专业的笔记助手，擅长将视频转录内容整理成笔记。"
        return SystemMessage(content=prompt)

    async def video_analyse(self, transcript_text: str):
        system_message = self.render_rel_system_message()
        human_msg = self.render_rel_human_message(transcript_text=transcript_text)
        # logger.info(f"****Summerize Video Analyse Human Message Input****: {human_msg}")
        try:
            response = await self.llm.ainvoke([system_message, human_msg])
            res = response.content
            logger.info(f"****Summerize Video Analyse LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize Video Analyse LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员"
        return res
    # TODO： USING MCP

    async def video_analyse_with_bvid(self, bvid: str, if_save: bool = True):
        if os.path.exists(f"{self.output_dir}/video_analyse/{bvid}.md"):
            with open(f"{self.output_dir}/video_analyse/{bvid}.md", "r", encoding="utf-8") as f:
                return f.read()
        transcript_text = await video_source_downloader.download_subtitle(bvid=bvid)
        res = await self.video_analyse(transcript_text)
        if if_save:
            with open(f"{self.output_dir}/video_analyse/{bvid}.md", "w", encoding="utf-8") as f:
                f.write(res)
        return res

    async def run(self, bvid: str, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        res = await self.video_analyse_with_bvid(bvid)
        return res

video_analyser = VideoAnalyse()
