"""
<PERSON><PERSON><PERSON> to create indexes for dynamic danmu tables.
This script should be run whenever new danmu_table_{room_id} tables are created.
"""

import asyncio
import asyncpg
from typing import List
from vups.logger import logger
from vups_server.sql.db_pool import get_connection


async def get_existing_danmu_tables() -> List[str]:
    """Get list of existing danmu_table_* tables."""
    try:
        async with get_connection() as conn:
            query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'danmu_table_%'
            """
            results = await conn.fetch(query)
            return [row['table_name'] for row in results]
    except Exception as e:
        logger.error(f"Error getting danmu tables: {e}")
        return []


async def create_danmu_table_indexes(table_name: str) -> bool:
    """Create indexes for a specific danmu table."""
    try:
        async with get_connection() as conn:
            indexes = [
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_timestamp ON {table_name} (timestamp);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime ON {table_name} (datetime);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_live_id ON {table_name} (live_id);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_room_timestamp ON {table_name} (room_id, timestamp);",
                f"CREATE INDEX IF NOT EXISTS idx_{table_name}_room_datetime ON {table_name} (room_id, datetime);"
            ]
            
            for index_sql in indexes:
                await conn.execute(index_sql)
                logger.info(f"Created index for {table_name}: {index_sql}")
            
            return True
    except Exception as e:
        logger.error(f"Error creating indexes for {table_name}: {e}")
        return False


async def create_all_danmu_indexes():
    """Create indexes for all existing danmu tables."""
    tables = await get_existing_danmu_tables()
    logger.info(f"Found {len(tables)} danmu tables")
    
    success_count = 0
    for table in tables:
        if await create_danmu_table_indexes(table):
            success_count += 1
    
    logger.info(f"Successfully created indexes for {success_count}/{len(tables)} tables")


def get_room_id_from_table_name(table_name: str) -> str:
    """Extract room_id from danmu table name."""
    return table_name.replace('danmu_table_', '')


async def create_indexes_for_room(room_id: str) -> bool:
    """Create indexes for a specific room's danmu table."""
    table_name = f"danmu_table_{room_id}"
    
    # Check if table exists first
    try:
        async with get_connection() as conn:
            exists = await conn.fetchval(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = $1
                );
                """,
                table_name
            )
            
            if not exists:
                logger.warning(f"Table {table_name} does not exist")
                return False
            
            return await create_danmu_table_indexes(table_name)
    except Exception as e:
        logger.error(f"Error checking table existence for {table_name}: {e}")
        return False


if __name__ == "__main__":
    # Run this script to create indexes for all existing danmu tables
    asyncio.run(create_all_danmu_indexes())
