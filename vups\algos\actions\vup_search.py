
from datetime import datetime
from langchain.schema import HumanMessage

from vups.algos.prompts import load_prompt
from vups.base.action.abstract_action import Action
import vups.utils as U
from vups.logger import logger
from vups_server.sql.db_pool import get_connection


class VupSearch(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
    ):
        super().__init__(llm_name, "vup_search")
        self.token_max = 5000

    def render_rel_system_message(self):
        prompt = load_prompt("vup_search")
        return HumanMessage(content=prompt)

    def render_rel_human_message(self, question: str):
        prompt = f"你是一个专业的vup虚拟偶像领域的专家助手，负责使用工具帮忙查询用户有关问题并给出回答。用户的问题是：{question}"
        return HumanMessage(content=prompt)

    async def vup_search(self, question: str):

        # logger.info(f"****Summerize vup_search LLM Input****: {doc}")
        system_msg = self.render_rel_system_message()
        human_msg = self.render_rel_human_message(question)
        logger.debug(f"****Summerize vup_search LLM Input****: {human_msg.content}")
        try:
            response = await self.mcp_client.ainvoke([system_msg, human_msg])
            res = response.content
            logger.info(f"****Summerize vup_search LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize vup_search LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员"
        return res

    async def run(self, question: str, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        message = await self.vup_search(question)
        return message

# TODO: /board/relations: fetch all relations to graph

vup_searcher = VupSearch()
