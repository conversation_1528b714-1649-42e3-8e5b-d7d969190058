package handlers

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"vups_backend/internal/models"
	"vups_backend/internal/services"
)

// VUPSearchHandler handles VUP search requests
type VUPSearchHandler struct {
	service *services.VUPSearchService
}

// NewVUPSearchHandler creates a new VUP search handler
func NewVUPSearchHandler(service *services.VUPSearchService) *VUPSearchHandler {
	return &VUPSearchHandler{
		service: service,
	}
}

// Search handles VUP search requests
// @Summary Search VUP data
// @Description Perform a VUP search query
// @Tags VUP Search
// @Accept json
// @Produce json
// @Param request body models.VUPSearchRequest true "Search request"
// @Success 200 {object} models.APIResponse{data=models.VUPSearchResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/vup/search [post]
func (h *VUPSearchHandler) Search(c *gin.Context) {
	var request models.VUPSearchRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	var response *models.VUPSearchResponse
	var err error

	if request.Stream {
		// For streaming requests, redirect to stream endpoint
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Use /api/v1/vup/search/stream for streaming requests",
		})
		return
	}

	response, err = h.service.Search(ctx, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Search failed",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    response,
	})
}

// SearchAsync handles async VUP search requests
// @Summary Async VUP search
// @Description Perform an async VUP search query using Celery
// @Tags VUP Search
// @Accept json
// @Produce json
// @Param request body models.VUPSearchRequest true "Search request"
// @Success 202 {object} models.APIResponse{data=models.VUPSearchResponse}
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/vup/search/async [post]
func (h *VUPSearchHandler) SearchAsync(c *gin.Context) {
	var request models.VUPSearchRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	response, err := h.service.SearchAsync(ctx, &request)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to start async search",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusAccepted, models.APIResponse{
		Code:    http.StatusAccepted,
		Message: "Task started",
		Data:    response,
	})
}

// SearchStream handles streaming VUP search requests
// @Summary Stream VUP search
// @Description Perform a VUP search query with streaming response
// @Tags VUP Search
// @Accept json
// @Produce text/event-stream
// @Param request body models.VUPSearchRequest true "Search request"
// @Success 200 {string} string "Server-Sent Events stream"
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/vup/search/stream [post]
func (h *VUPSearchHandler) SearchStream(c *gin.Context) {
	var request models.VUPSearchRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Invalid request format",
			Details: err.Error(),
		})
		return
	}

	// Set headers for Server-Sent Events
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Minute)
	defer cancel()

	// Create event channel
	eventChan := make(chan *models.VUPSearchStreamEvent, 100)

	// Start streaming in goroutine
	go func() {
		if err := h.service.SearchStream(ctx, &request, eventChan); err != nil {
			// Send error event if not already closed
			select {
			case eventChan <- &models.VUPSearchStreamEvent{
				Type:    "error",
				Content: err.Error(),
				Done:    true,
			}:
			default:
			}
		}
	}()

	// Stream events to client
	c.Stream(func(w gin.ResponseWriter) bool {
		select {
		case event, ok := <-eventChan:
			if !ok {
				return false
			}
			
			// Format as Server-Sent Event
			if event.Type != "" {
				c.SSEvent(event.Type, event)
			} else {
				c.SSEvent("message", event)
			}
			
			return !event.Done
		case <-ctx.Done():
			return false
		}
	})
}

// GetTaskResult gets the result of an async task
// @Summary Get task result
// @Description Get the result of an async VUP search task
// @Tags VUP Search
// @Produce json
// @Param task_id path string true "Task ID"
// @Success 200 {object} models.APIResponse{data=models.TaskStatus}
// @Failure 400 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/vup/search/task/{task_id} [get]
func (h *VUPSearchHandler) GetTaskResult(c *gin.Context) {
	taskID := c.Param("task_id")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Code:    http.StatusBadRequest,
			Message: "Task ID is required",
		})
		return
	}

	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	result, err := h.service.GetTaskResult(ctx, taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Code:    http.StatusInternalServerError,
			Message: "Failed to get task result",
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    http.StatusOK,
		Message: "Success",
		Data:    result,
	})
}
