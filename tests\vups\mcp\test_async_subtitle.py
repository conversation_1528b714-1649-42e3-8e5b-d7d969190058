"""
Test script for the new async subtitle generation system
"""
import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from vups.algos.tools.subtitle_task_manager import task_manager, TaskStatus
from vups.logger import logger


async def test_async_subtitle_generation():
    """Test the async subtitle generation workflow"""
    
    # Test video BV ID (use a short video for testing)
    test_bvid = "BV1NXaJzhEFD"  # Replace with a known short video
    
    logger.info("=== Testing Async Subtitle Generation ===")
    
    # Step 1: Create a task
    logger.info(f"Creating subtitle task for video {test_bvid}")
    task_id = task_manager.create_task(test_bvid, "tests/vups/data/async_test_subtitle.txt")
    logger.info(f"Task created with ID: {task_id}")
    
    # Step 2: Monitor progress
    logger.info("Monitoring task progress...")
    max_wait_time = 300  # 5 minutes max
    wait_time = 0
    
    while wait_time < max_wait_time:
        progress = task_manager.get_task_status(task_id)
        if not progress:
            logger.error("Task not found!")
            break
            
        logger.info(f"Status: {progress.status.value}, Progress: {progress.progress_percent:.1f}%, Step: {progress.current_step}")
        
        if progress.status == TaskStatus.COMPLETED:
            logger.info("Task completed successfully!")
            logger.info(f"Result: {progress.result}")
            break
        elif progress.status == TaskStatus.FAILED:
            logger.error(f"Task failed: {progress.error_message}")
            break
        elif progress.status == TaskStatus.CANCELLED:
            logger.info("Task was cancelled")
            break
            
        await asyncio.sleep(5)  # Check every 5 seconds
        wait_time += 5
    
    if wait_time >= max_wait_time:
        logger.warning("Test timed out - cancelling task")
        task_manager.cancel_task(task_id)


async def test_mcp_endpoints():
    """Test the MCP endpoints directly"""
    logger.info("=== Testing MCP Endpoints ===")
    
    # Import the MCP functions
    from vups.mcp.server import (
        start_video_subtitle_task,
        get_subtitle_task_status,
        get_subtitle_task_result,
        cancel_subtitle_task
    )
    
    test_bvid = "BV1NXaJzhEFD"
    
    # Test 1: Start task
    logger.info("Testing start_video_subtitle_task...")
    result = await start_video_subtitle_task(test_bvid)
    logger.info(f"Start task result: {result}")
    task_id = result.get("task_id")
    
    if not task_id:
        logger.error("Failed to get task ID")
        return
    
    # Test 2: Check status
    logger.info("Testing get_subtitle_task_status...")
    for i in range(5):  # Check status 5 times
        status_result = await get_subtitle_task_status(task_id)
        logger.info(f"Status check {i+1}: {status_result}")
        
        if status_result.get("status") == "completed":
            # Test 3: Get result
            logger.info("Testing get_subtitle_task_result...")
            final_result = await get_subtitle_task_result(task_id)
            logger.info(f"Final result: {final_result}")
            break
        elif status_result.get("status") == "failed":
            logger.error(f"Task failed: {status_result.get('error_message')}")
            break
            
        await asyncio.sleep(10)  # Wait 10 seconds between checks
    else:
        # Test 4: Cancel task if still running
        logger.info("Testing cancel_subtitle_task...")
        cancel_result = await cancel_subtitle_task(task_id)
        logger.info(f"Cancel result: {cancel_result}")


async def test_progress_callback():
    """Test progress callback functionality"""
    logger.info("=== Testing Progress Callback ===")
    
    from vups.algos.tools.subtitle_task_manager import ProgressCallback, TaskStatus
    
    # Create a mock task manager for testing
    class MockTaskManager:
        def __init__(self):
            self.progress_updates = []
            self.status_updates = []
        
        def update_task_progress(self, task_id, progress_percent, current_step, estimated_remaining=None):
            self.progress_updates.append({
                "task_id": task_id,
                "progress": progress_percent,
                "step": current_step,
                "estimated": estimated_remaining
            })
            logger.info(f"Progress update: {progress_percent:.1f}% - {current_step}")
        
        def update_task_status(self, task_id, status, error_message=None):
            self.status_updates.append({
                "task_id": task_id,
                "status": status,
                "error": error_message
            })
            logger.info(f"Status update: {status.value}" + (f" - {error_message}" if error_message else ""))
    
    mock_manager = MockTaskManager()
    callback = ProgressCallback("test-task-123", mock_manager)
    
    # Simulate progress updates
    callback.update_progress(0, "Starting...")
    callback.update_progress(25, "Processing audio...")
    callback.update_progress(50, "Transcribing...", 120)
    callback.update_progress(75, "Finalizing...")
    callback.update_status(TaskStatus.COMPLETED)
    
    logger.info(f"Total progress updates: {len(mock_manager.progress_updates)}")
    logger.info(f"Total status updates: {len(mock_manager.status_updates)}")


async def main():
    """Run all tests"""
    logger.info("Starting async subtitle generation tests...")
    
    try:
        # Test 1: Progress callback functionality
        await test_progress_callback()
        
        # Test 2: MCP endpoints (comment out for quick testing)
        # await test_mcp_endpoints()
        
        # Test 3: Full async workflow (comment out for quick testing)
        # await test_async_subtitle_generation()
        
        logger.info("All tests completed!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
