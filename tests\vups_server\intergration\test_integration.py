"""
Integration Test Script

Tests the refactored VUPs server system to ensure all functionality is preserved.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from vups.logger import logger
from vups_server.integration.round_robin_integration import get_system


async def test_system_initialization():
    """Test system initialization"""
    logger.info("Testing system initialization...")
    
    try:
        system = get_system()
        await system.initialize()
        
        status = system.get_system_status()
        logger.info(f"System initialized successfully")
        logger.info(f"Registered servers: {list(status['servers'].keys())}")
        logger.info(f"Registered tasks: {status['tasks']['registered_count']}")
        
        return True
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        return False


async def test_server_status():
    """Test server status retrieval"""
    logger.info("Testing server status retrieval...")
    
    try:
        system = get_system()
        
        for server_type, server in system.servers.items():
            if hasattr(server, 'get_server_status'):
                status = await server.get_server_status()
                logger.info(f"{server_type} server status: {status}")
            else:
                logger.warning(f"{server_type} server has no status method")
        
        return True
    except Exception as e:
        logger.error(f"Server status test failed: {e}")
        return False


async def test_scheduler_functionality():
    """Test scheduler functionality"""
    logger.info("Testing scheduler functionality...")
    
    try:
        system = get_system()
        
        # Start the scheduler
        await system.start()
        
        # Check scheduler status
        scheduler_status = system.scheduler.get_status()
        logger.info(f"Scheduler status: {scheduler_status}")
        
        # Wait a bit to see if tasks are being processed
        await asyncio.sleep(5)
        
        # Check updated status
        updated_status = system.scheduler.get_status()
        logger.info(f"Updated scheduler status: {updated_status}")
        
        return True
    except Exception as e:
        logger.error(f"Scheduler test failed: {e}")
        return False


async def test_health_check():
    """Test system health check"""
    logger.info("Testing system health check...")
    
    try:
        system = get_system()
        health = await system.health_check()
        
        logger.info(f"Health check result: {health['overall_status']}")
        
        for check_name, check_result in health['checks'].items():
            logger.info(f"{check_name}: {check_result['status']}")
        
        return health['overall_status'] in ['healthy', 'warning']
    except Exception as e:
        logger.error(f"Health check test failed: {e}")
        return False


async def test_manual_task_execution():
    """Test manual task execution"""
    logger.info("Testing manual task execution...")
    
    try:
        system = get_system()
        
        # Test creator server function
        if 'creator' in system.servers:
            result = await system.execute_manual_task(
                'creator', 
                'get_server_status'
            )
            logger.info(f"Creator manual task result: {result}")
        
        # Test user server function
        if 'user' in system.servers:
            result = await system.execute_manual_task(
                'user', 
                'get_server_status'
            )
            logger.info(f"User manual task result: {result}")
        
        # Test live server function
        if 'live' in system.servers:
            result = await system.execute_manual_task(
                'live', 
                'get_server_status'
            )
            logger.info(f"Live manual task result: {result}")
        
        return True
    except Exception as e:
        logger.error(f"Manual task execution test failed: {e}")
        return False


async def test_task_statistics():
    """Test task statistics functionality"""
    logger.info("Testing task statistics...")
    
    try:
        from vups_server.base.task_decorators import get_task_statistics
        
        stats = get_task_statistics()
        logger.info(f"Task statistics: {json.dumps(stats, indent=2)}")
        
        return True
    except Exception as e:
        logger.error(f"Task statistics test failed: {e}")
        return False


async def test_cookie_management():
    """Test cookie management functionality"""
    logger.info("Testing cookie management...")
    
    try:
        system = get_system()
        cookie_status = system.cookie_manager.get_status()
        
        logger.info(f"Cookie manager status: {json.dumps(cookie_status, indent=2)}")
        
        # Test cookie refresh
        enabled_tasks = cookie_status.get('enabled_tasks', [])
        for task_type in enabled_tasks:
            try:
                await system.cookie_manager.refresh_cookie_for_task(task_type)
                logger.info(f"Successfully refreshed cookie for {task_type}")
            except Exception as e:
                logger.warning(f"Failed to refresh cookie for {task_type}: {e}")
        
        return True
    except Exception as e:
        logger.error(f"Cookie management test failed: {e}")
        return False


async def run_all_tests():
    """Run all integration tests"""
    logger.info("Starting VUPs Round-Robin System Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("System Initialization", test_system_initialization),
        ("Server Status", test_server_status),
        ("Scheduler Functionality", test_scheduler_functionality),
        ("Health Check", test_health_check),
        ("Manual Task Execution", test_manual_task_execution),
        ("Task Statistics", test_task_statistics),
        ("Cookie Management", test_cookie_management),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Running {test_name} Test ---")
        try:
            result = await test_func()
            results[test_name] = result
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name} Test: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name} Test: FAILED with exception: {e}")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The refactoring was successful.")
    else:
        logger.warning(f"⚠️  {total - passed} tests failed. Please review the issues.")
    
    # Cleanup
    try:
        system = get_system()
        if system.is_running:
            await system.stop()
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
    
    return passed == total


async def main():
    """Main test runner"""
    try:
        success = await run_all_tests()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Test runner failed: {e}")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
