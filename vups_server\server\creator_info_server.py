import asyncio
import json
import math
import time
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from vups.logger import logger
from vups_server.sql.sentence import creator_sql
from vups_server.base.server_base import BaseServer
from vups_server.base.task_decorators import (
    pull_frequency, pull_schedule, round_robin_task,
    use_task_cookie, retry_on_failure, TaskPriority
)

class CreatorInfoServer(BaseServer):
    def __init__(self, uid="401315430", credential=None, db_conn=None):
        # Initialize base server with creator task type
        super().__init__(task_type="creator", uid=uid, credential=credential, db_conn=db_conn)

    async def initialize_async(self):
        await self._create_tables()

    async def _create_tables(self):
        """
        Create database tables and views.
        """
        logger.info("Creating database tables if not exists...")
        await self._execute_sql(creator_sql.create_key_data_overview_table_sql)
        await self._execute_sql(creator_sql.create_weekly_metrics_table_sql)
        await self._execute_sql(creator_sql.create_weekly_metrics_indexes_sql)
        await self._execute_sql(creator_sql.create_weekly_key_data_overview_table_sql)
        await self._execute_sql(creator_sql.create_weekly_play_analyze_table_sql)
        await self._execute_sql(creator_sql.create_weekly_attention_analyze_table_sql)
        await self._execute_sql(creator_sql.create_weekly_archive_analyze_table_sql)
        await self._execute_sql(creator_sql.create_key_elec_num_table_sql)
        await self._execute_sql(creator_sql.create_key_video_compare_table_sql)
        # await self._execute_sql(creator_sql.create_key_video_archive_table_sql)

        logger.info("Database table and view creation complete.")

    async def get_server_status(self):
        """Get current server status"""
        return {
            'server_type': 'creator',
            'uid': self.uid,
            'credential_valid': self.validate_credential(),
            'task_type': self.task_type
        }

    async def _insert_normalized_metrics(self, report_type, data, current_timestamp, current_datetime, tip=None):
        """
        Helper function to insert data into the normalized weekly_metrics_table.

        Args:
            report_type: 'overview', 'play_analyze', 'attention_analyze'
            data: Dictionary containing metric categories and their data
            current_timestamp: Current timestamp
            current_datetime: Current datetime
            tip: Optional tip text for certain report types
        """
        records = []

        for metric_category, metric_data in data.items():
            if metric_data and isinstance(metric_data, dict):
                additional_data = {}
                if tip and metric_category == list(data.keys())[0]:  # Add tip to first metric
                    additional_data['tip'] = tip

                record = (
                    self.uid,
                    report_type,
                    metric_category,
                    metric_data.get('amount'),
                    metric_data.get('amount_pass_per'),
                    metric_data.get('amount_last'),
                    metric_data.get('amount_last_pass_per'),
                    metric_data.get('amount_change'),
                    metric_data.get('amount_med'),
                    metric_data.get('date'),
                    json.dumps(metric_data.get('tendency_list')),
                    json.dumps(additional_data) if additional_data else None,
                    current_timestamp,
                    current_datetime
                )
                records.append(record)

        if records:
            await self._insert_data(creator_sql.insert_weekly_metrics_table_sql, records, "weekly_metrics_table")

#   ----------------------------------  Overview  --------------------------------------

    @pull_schedule(hour=3, minute=0, priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_key_data_overview(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/overview/stat/num?period=-1&tab=0&tmid=&t=1757493254709

        args: cookie
            period = (-1 yestoday 0 7day 1 30day 2 90day 3 total)

        returns: 核心数据概览.
        {
            "data": {
                "play": 18702,
                "play_last": 22117,
                "visitor": 3448,
                "visitor_last": 3052,
                "fan": -120,
                "fan_last": -77,
                "like": 0,
                "like_last": 0,
                "fav": 0,
                "fav_last": 0,
                "coin": 0,
                "coin_last": 0,
                "dm": 0,
                "dm_last": 0,
                "comment": 0,
                "comment_last": 0,
                "share": 0,
                "share_last": 0,
                "elec": 0,
                "elec_last": 0,
                "vt": 0,
                "vt_last": 0,
                "log_date": 20250909
            }
        }

        """
        url = "https://member.bilibili.com/x/web/data/v2/overview/stat/num"
        result1 = await self._fetch_with_retry(url=url, params={"period": "-1", "tab": "0"})
        result2 = await self._fetch_with_retry(url=url, params={"period": "-1", "tab": "1"})
        result3 = await self._fetch_with_retry(url=url, params={"period": "-1", "tab": "2"})

        if result1 and result1.get('code') == 0 and result1.get('data'):

            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            data1 = result1['data']
            data2 = result2['data']
            data3 = result3['data']

            record = (
                self.uid,
                data1.get('play'),
                data1.get('play_last'),
                data1.get('visitor'),
                data1.get('visitor_last'),
                data1.get('fan'),
                data1.get('fan_last'),
                data2.get('like'),
                data2.get('like_last'),
                data2.get('fav'),
                data2.get('fav_last'),
                data2.get('coin'),
                data2.get('coin_last'),
                data3.get('dm'),
                data3.get('dm_last'),
                data3.get('comment'),
                data3.get('comment_last'),
                data3.get('share'),
                data3.get('share_last'),
                data1.get('log_date'),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_key_data_overview_table_sql, [record], "key_data_overview_table")

    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_key_elec_num(self):
        """
        url: https://member.bilibili.com/x/web/data/v3/overview/elec/num?period=1&tmid=&t=1757518634251

        args: cookie
            period = 0 7day

        returns: 核心数据概览.
        {
            "data": {
                "all_total": 266045,
                "month_total": 263820,
                "custom_total": 2225,
                "month_switch": true
            }
        }

        """
        url = "https://member.bilibili.com/x/web/data/v3/overview/elec/num"
        result = await self._fetch_with_retry(url=url, params={"period": "1"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            record = (
                self.uid,
                data.get('all_total'),
                data.get('month_total'),
                data.get('custom_total'),
                data.get('month_switch'),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_key_elec_num_table_sql, [record], "key_elec_num_table")

        return result

    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_key_video_compare(self):
        """
        url: https://member.bilibili.com/x/web/data/archive_diagnose/compare?size=10&tmid=&t=1757518634253

        args: cookie
            size = number of videos to compare

        returns:
        {
            "data": {
                "list": [
                    {
                        "aid": 115179413047586,
                        "bvid": "BV1reHXzxEqx",
                        "cover": "http://i1.hdslb.com/bfs/archive/0ee91e28240c6e2d3a6f1d156ae8e99e2344cb8c.jpg",
                        "title": "重生之我在《盛世天下》里当皇帝！【录播合集】",
                        "pubtime": 1757498736,
                        "duration": 10912,
                        "stat": {
                            "not_ready_field": [
                                "active_fans_rate",
                                "interact_fan_rate",
                                "play_fan_rate",
                                "play_fan_rate_med",
                                "avg_play_time_int",
                                "play_viewer_rate_med",
                                "tm_fan_rate",
                                "crash_rate_med",
                                "tm_fan_simi_rate_med",
                                "tm_fan_pass_rate",
                                "crash_fan_rate",
                                "interact_rate_med",
                                "interact_fan_simi_rate_med",
                                "interact_viewer_simi_rate_med",
                                "play_trans_fan_rate_med",
                                "full_play_ratio",
                                "tm_rate_med",
                                "tm_fan_star",
                                "tm_viewer_rate",
                                "avg_play_time",
                                "interact_viewer_rate",
                                "total_new_attention_cnt",
                                "play_trans_fan_rate",
                                "crash_rate",
                                "crash_viewer_rate",
                                "crash_fan_simi_rate_med",
                                "crash_viewer_simi_rate_med",
                                "unfollow",
                                "tm_viewer_star",
                                "active_fans_med",
                                "tm_viewer_pass_rate",
                                "play_viewer_rate",
                                "tm_viewer_simi_rate_med"
                            ],
                            "play": 7730,
                            "vt": 0,
                            "full_play_ratio": 0,
                            "play_viewer_rate": 0,
                            "play_viewer_rate_med": 0,
                            "play_fan_rate": 0,
                            "play_fan_rate_med": 0,
                            "active_fans_rate": 0,
                            "active_fans_med": 0,
                            "tm_rate": 248,
                            "tm_rate_med": 0,
                            "tm_fan_simi_rate_med": 0,
                            "tm_viewer_simi_rate_med": 0,
                            "tm_fan_rate": 0,
                            "tm_viewer_rate": 0,
                            "tm_pass_rate": 5194,
                            "tm_fan_pass_rate": 0,
                            "tm_viewer_pass_rate": 0,
                            "crash_rate": 0,
                            "crash_rate_med": 0,
                            "crash_fan_simi_rate_med": 0,
                            "crash_viewer_simi_rate_med": 0,
                            "crash_fan_rate": 0,
                            "crash_viewer_rate": 0,
                            "interact_rate": 2680,
                            "interact_rate_med": 0,
                            "interact_fan_simi_rate_med": 0,
                            "interact_viewer_simi_rate_med": 0,
                            "interact_fan_rate": 0,
                            "interact_viewer_rate": 0,
                            "avg_play_time": 0,
                            "avg_play_time_int": 0,
                            "total_new_attention_cnt": 0,
                            "play_trans_fan_rate": 0,
                            "play_trans_fan_rate_med": 0,
                            "like": 1234,
                            "comment": 157,
                            "dm": 18,
                            "fav": 180,
                            "coin": 352,
                            "share": 24,
                            "unfollow": 0,
                            "tm_star": 28,
                            "tm_viewer_star": 0,
                            "tm_fan_star": 0,
                            "crash_p50": 3059,
                            "crash_viewer_p50": 2890,
                            "crash_fan_p50": 3245,
                            "interact_p50": 790,
                            "interact_viewer_p50": 488,
                            "interact_fan_p50": 1510,
                            "play_trans_fan_p50": 36
                        },
                        "is_only_self": false,
                        "hour_stat": {
                            "not_ready_field": null,
                            "play": 7730,
                            "vt": 0,
                            "like": 1234,
                            "comment": 157,
                            "dm": 18,
                            "fav": 180,
                            "coin": 352,
                            "share": 24,
                            "tm_pass_rate": 5194,
                            "interact_rate": 2680,
                            "tm_star": 28
                        }
                    },
                    {
                        "aid": 115161696374129,
                        "bvid": "BV1eMYVzcEDq",
                        "cover": "http://i0.hdslb.com/bfs/archive/947401a3ad7ddc907965acc98b0fe427a198562c.jpg",
                        "title": "周末进行曲！一起《美美Sunday》！",
                        "pubtime": 1757239200,
                        "duration": 18,
                        "stat": {
                            "not_ready_field": null,
                            "play": 38047,
                            "vt": 0,
                            "full_play_ratio": 5438,
                            "play_viewer_rate": 4170,
                            "play_viewer_rate_med": 5640,
                            "play_fan_rate": 189,
                            "play_fan_rate_med": 159,
                            "active_fans_rate": 189,
                            "active_fans_med": 159,
                            "tm_rate": 663,
                            "tm_rate_med": 292,
                            "tm_fan_simi_rate_med": 263,
                            "tm_viewer_simi_rate_med": 263,
                            "tm_fan_rate": 733,
                            "tm_viewer_rate": 638,
                            "tm_pass_rate": 9025,
                            "tm_fan_pass_rate": 8380,
                            "tm_viewer_pass_rate": 8657,
                            "crash_rate": 4818,
                            "crash_rate_med": 3483,
                            "crash_fan_simi_rate_med": 3975,
                            "crash_viewer_simi_rate_med": 2761,
                            "crash_fan_rate": 5074,
                            "crash_viewer_rate": 4159,
                            "interact_rate": 1476,
                            "interact_rate_med": 735,
                            "interact_fan_simi_rate_med": 1102,
                            "interact_viewer_simi_rate_med": 418,
                            "interact_fan_rate": 2342,
                            "interact_viewer_rate": 299,
                            "avg_play_time": 0,
                            "avg_play_time_int": 0,
                            "total_new_attention_cnt": 17,
                            "play_trans_fan_rate": 11,
                            "play_trans_fan_rate_med": 17,
                            "like": 3663,
                            "comment": 211,
                            "dm": 29,
                            "fav": 547,
                            "coin": 1083,
                            "share": 74,
                            "unfollow": 45,
                            "tm_star": 50,
                            "tm_viewer_star": 50,
                            "tm_fan_star": 45,
                            "crash_p50": 3059,
                            "crash_viewer_p50": 2890,
                            "crash_fan_p50": 3245,
                            "interact_p50": 790,
                            "interact_viewer_p50": 488,
                            "interact_fan_p50": 1510,
                            "play_trans_fan_p50": 36
                        },
                        "is_only_self": false,
                        "hour_stat": {
                            "not_ready_field": null,
                            "play": 36258,
                            "vt": 0,
                            "like": 3504,
                            "comment": 205,
                            "dm": 29,
                            "fav": 525,
                            "coin": 1050,
                            "share": 71,
                            "tm_pass_rate": 9035,
                            "interact_rate": 1484,
                            "tm_star": 50
                        }
                    },
                    ...
                ]
            }
        }
        """
        url = "https://member.bilibili.com/x/web/data/archive_diagnose/compare"
        result = await self._fetch_with_retry(url=url, params={"size": "10"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            video_list = data.get('list', [])

            if not video_list:
                return result

            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            # Process each video in the comparison list
            records = []
            for video in video_list:
                record = self._create_video_compare_record(video, current_timestamp, current_datetime)
                if record:
                    records.append(record)

            # Bulk insert all records
            if records:
                await self._insert_data(creator_sql.insert_key_video_compare_table_sql, records, "key_video_compare_table")

        return result

    # @pull_schedule(hour=3, minute=0, priority=TaskPriority.NORMAL)
    # @use_task_cookie(task_type="creator")
    # @retry_on_failure(max_retries=1)
    async def fetch_key_video_archive(self):
        """
        url: https://member.bilibili.com/x/web/data/archive/index?pn=1&ps=20&scene=archive_compare&order=0&tmid=&t=1757518634650

        args: cookie
            pn = page number
            ps = page size
            scene = archive_compare
            order = 0

        returns: Video archive data with pagination.
        """
        url = "https://member.bilibili.com/x/web/data/archive/index"
        base_params = {"ps": "20", "scene": "archive_compare", "order": "0"}

        # First, get the first page to determine total count
        first_result = await self._fetch_with_retry(url=url, params={**base_params, "pn": "1"})

        if not (first_result and first_result.get('code') == 0 and first_result.get('data')):
            return first_result

        first_data = first_result['data']
        pager = first_data.get('pager', {})
        total = pager.get('total', 0)
        ps = pager.get('ps', 20)

        if total == 0:
            return first_result

        # Calculate total pages
        total_pages = math.ceil(total / ps)

        # Collect all video records
        all_records = []
        current_timestamp = self.get_current_timestamp()
        current_datetime = self.get_current_datetime_midnight()

        # Process first page
        if first_data.get('list'):
            for video in first_data['list']:
                record = self._create_video_archive_record(video, current_timestamp, current_datetime)
                if record:
                    all_records.append(record)

        # Fetch remaining pages
        for page_num in range(2, total_pages + 1):
            result = await self._fetch_with_retry(url=url, params={**base_params, "pn": str(page_num)})

            if result and result.get('code') == 0 and result.get('data'):
                data = result['data']
                if data.get('list'):
                    for video in data['list']:
                        record = self._create_video_archive_record(video, current_timestamp, current_datetime)
                        if record:
                            all_records.append(record)

        # Bulk insert all records
        if all_records:
            await self._insert_data(creator_sql.insert_key_video_archive_table_sql, all_records, "key_video_archive_table")

        return first_result

    def _create_video_archive_record(self, video, current_timestamp, current_datetime):
        """
        Helper method to create a video archive record from API response data.

        Args:
            video: Video data from API response
            current_timestamp: Current timestamp
            current_datetime: Current datetime

        Returns:
            Tuple representing a database record, or None if invalid data
        """
        if not video or not video.get('bvid'):
            return None

        real_stat = video.get('real_stat', {})

        record = (
            self.uid,
            str(video.get('aid', '')),
            video.get('bvid'),
            video.get('title', ''),
            video.get('cover', ''),
            video.get('ctime'),
            video.get('pubtime'),
            video.get('duration'),
            real_stat.get('play'),
            real_stat.get('reply'),
            real_stat.get('likes'),
            real_stat.get('vt'),
            str(real_stat.get('fans', '')),
            str(real_stat.get('full_play_ratio', '')),
            video.get('is_only_self', False),
            current_timestamp,
            current_datetime
        )

        return record

    def _create_video_compare_record(self, video, current_timestamp, current_datetime):
        """
        Helper method to create a video comparison record from API response data.

        Args:
            video: Video data from API response
            current_timestamp: Current timestamp
            current_datetime: Current datetime

        Returns:
            Tuple representing a database record, or None if invalid data
        """
        if not video or not video.get('bvid'):
            return None

        stat = video.get('stat', {})
        hour_stat = video.get('hour_stat', {})

        record = (
            self.uid,
            str(video.get('aid', '')),
            video.get('bvid'),
            video.get('title', ''),
            video.get('cover', ''),
            video.get('pubtime'),
            video.get('duration'),
            video.get('is_only_self', False),
            # Main statistics
            stat.get('play'),
            stat.get('vt'),
            stat.get('like'),
            stat.get('comment'),
            stat.get('dm'),
            stat.get('fav'),
            stat.get('coin'),
            stat.get('share'),
            stat.get('unfollow'),
            # Performance metrics
            stat.get('full_play_ratio'),
            stat.get('play_viewer_rate'),
            stat.get('play_fan_rate'),
            stat.get('active_fans_rate'),
            stat.get('tm_rate'),
            stat.get('tm_pass_rate'),
            stat.get('crash_rate'),
            stat.get('interact_rate'),
            stat.get('avg_play_time'),
            stat.get('total_new_attention_cnt'),
            stat.get('play_trans_fan_rate'),
            stat.get('tm_star'),
            # P50 metrics
            stat.get('crash_p50'),
            stat.get('crash_viewer_p50'),
            stat.get('crash_fan_p50'),
            stat.get('interact_p50'),
            stat.get('interact_viewer_p50'),
            stat.get('interact_fan_p50'),
            stat.get('play_trans_fan_p50'),
            # Hour statistics
            hour_stat.get('play'),
            hour_stat.get('vt'),
            hour_stat.get('like'),
            hour_stat.get('comment'),
            hour_stat.get('dm'),
            hour_stat.get('fav'),
            hour_stat.get('coin'),
            hour_stat.get('share'),
            hour_stat.get('tm_pass_rate'),
            hour_stat.get('interact_rate'),
            hour_stat.get('tm_star'),
            current_timestamp,
            current_datetime
        )

        return record


#   ----------------------------------  Overview  --------------------------------------

#   ----------------------------------  Weekly  ---------------------------------------
    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_weekly_key_data_overview(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/account_diagnose/overview?period=0&tmid=&t=*************

        args: cookie
            period = 0 7day
            tab = (0 play 1 interact 2 income)

        returns: data.数据周报的核心数据
            {
                "data": {
                    "play_cnt": {
                        "amount": 394273,
                        "amount_pass_per": 5524,
                        "amount_last": 592434,
                        "amount_last_pass_per": 5637,
                        "amount_change": -198161,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "interact_rate": {
                        "amount": 1354,
                        "amount_pass_per": 9189,
                        "amount_last": 1036,
                        "amount_last_pass_per": 8410,
                        "amount_change": 3069,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "net_attention_cnt": {
                        "amount": 2185,
                        "amount_pass_per": 5014,
                        "amount_last": 1856,
                        "amount_last_pass_per": 3883,
                        "amount_change": 1772,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "interact_fans_per": {
                        "amount": 1392,
                        "amount_pass_per": 8645,
                        "amount_last": 1219,
                        "amount_last_pass_per": 8014,
                        "amount_change": 1419,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    },
                    "avs_num": {
                        "amount": 6,
                        "amount_pass_per": 8228,
                        "amount_last": 5,
                        "amount_last_pass_per": 7811,
                        "amount_change": 1,
                        "amount_med": 0,
                        "date": ********,
                        "tendency_list": null
                    }
                }
            }

        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/overview"
        result = await self._fetch_with_retry(url=url, params={"period": "0"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            # Insert data using normalized structure
            await self._insert_normalized_metrics('overview', data, current_timestamp, current_datetime)


    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_weekly_play_analyze(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/account_diagnose/play_analyze?period=0&tmid=&t=*************

        args: cookie
            period = 0 7day

        returns: data.数据周报的播放分析
        {
            "data": {
                "all_play": {
                    "amount": 394273,
                    "amount_pass_per": 0,
                    "amount_last": 592434,
                    "amount_last_pass_per": 0,
                    "amount_change": -3344,
                    "amount_med": 313318,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 553650,
                            "amount_med": 496930,
                            "date": **********
                        },
                        {
                            "amount": 400060,
                            "amount_med": 465963,
                            "date": **********
                        },
                        {
                            "amount": 525279,
                            "amount_med": 464269,
                            "date": 1753632000
                        },
                        {
                            "amount": 1006103,
                            "amount_med": 447952,
                            "date": 1754236800
                        },
                        {
                            "amount": 485179,
                            "amount_med": 461634,
                            "date": 1754841600
                        },
                        {
                            "amount": 560282,
                            "amount_med": 442502,
                            "date": 1755446400
                        },
                        {
                            "amount": 592434,
                            "amount_med": 439967,
                            "date": 1756051200
                        },
                        {
                            "amount": 394273,
                            "amount_med": 313318,
                            "date": **********
                        }
                    ]
                },
                "viewer_play": {
                    "amount": 221188,
                    "amount_pass_per": 0,
                    "amount_last": 395746,
                    "amount_last_pass_per": 0,
                    "amount_change": -4410,
                    "amount_med": 213185,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 332301,
                            "amount_med": 342936,
                            "date": **********
                        },
                        {
                            "amount": 265440,
                            "amount_med": 319595,
                            "date": **********
                        },
                        {
                            "amount": 342377,
                            "amount_med": 317598,
                            "date": 1753632000
                        },
                        {
                            "amount": 747636,
                            "amount_med": 307845,
                            "date": 1754236800
                        },
                        {
                            "amount": 356510,
                            "amount_med": 314055,
                            "date": 1754841600
                        },
                        {
                            "amount": 352642,
                            "amount_med": 293779,
                            "date": 1755446400
                        },
                        {
                            "amount": 395746,
                            "amount_med": 294865,
                            "date": 1756051200
                        },
                        {
                            "amount": 221188,
                            "amount_med": 213185,
                            "date": **********
                        }
                    ]
                },
                "fan_play": {
                    "amount": 173085,
                    "amount_pass_per": 0,
                    "amount_last": 196688,
                    "amount_last_pass_per": 0,
                    "amount_change": -1200,
                    "amount_med": 82354,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 221349,
                            "amount_med": 118424,
                            "date": **********
                        },
                        {
                            "amount": 134620,
                            "amount_med": 116182,
                            "date": **********
                        },
                        {
                            "amount": 182902,
                            "amount_med": 109765,
                            "date": 1753632000
                        },
                        {
                            "amount": 258467,
                            "amount_med": 109063,
                            "date": 1754236800
                        },
                        {
                            "amount": 128669,
                            "amount_med": 106226,
                            "date": 1754841600
                        },
                        {
                            "amount": 207640,
                            "amount_med": 105524,
                            "date": 1755446400
                        },
                        {
                            "amount": 196688,
                            "amount_med": 100928,
                            "date": 1756051200
                        },
                        {
                            "amount": 173085,
                            "amount_med": 82354,
                            "date": **********
                        }
                    ]
                },
                "interact_fans_per": {
                    "amount": 1392,
                    "amount_pass_per": 8645,
                    "amount_last": 1219,
                    "amount_last_pass_per": 0,
                    "amount_change": 1419,
                    "amount_med": 339,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 1731,
                            "amount_med": 445,
                            "date": **********
                        },
                        {
                            "amount": 1001,
                            "amount_med": 432,
                            "date": **********
                        },
                        {
                            "amount": 1572,
                            "amount_med": 431,
                            "date": 1753632000
                        },
                        {
                            "amount": 1957,
                            "amount_med": 419,
                            "date": 1754236800
                        },
                        {
                            "amount": 1256,
                            "amount_med": 417,
                            "date": 1754841600
                        },
                        {
                            "amount": 1565,
                            "amount_med": 412,
                            "date": 1755446400
                        },
                        {
                            "amount": 1219,
                            "amount_med": 393,
                            "date": 1756051200
                        },
                        {
                            "amount": 1392,
                            "amount_med": 339,
                            "date": **********
                        }
                    ]
                },
                "tip": "总播放较上周下降33.4%，主要受[[游客播放]]的影响，游客播放是粉丝转化的前提，目前[[超过0.5%同类]]，稳住趋势才能良性涨粉"
            }
        }
        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/play_analyze"
        result = await self._fetch_with_retry(url=url, params={"period": "0"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            # Extract tip and remove it from data for normalized insertion
            tip = data.pop('tip', None)

            # Insert data using normalized structure
            await self._insert_normalized_metrics('play_analyze', data, current_timestamp, current_datetime, tip)

        return result


    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_weekly_attention_analyze(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/account_diagnose/attention_analyze?period=0&tmid=&t=*************

        args: cookie
            period = 0 7day

        returns: data.数据周报的涨粉分析
        {
            "data": {
                "net_attention_cnt": {
                    "amount": 2185,
                    "amount_pass_per": 0,
                    "amount_last": 0,
                    "amount_last_pass_per": 0,
                    "amount_change": 1772,
                    "amount_med": 737,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 6699,
                            "amount_med": 1351,
                            "date": **********
                        },
                        {
                            "amount": 1302,
                            "amount_med": 1273,
                            "date": **********
                        },
                        {
                            "amount": 3627,
                            "amount_med": 1197,
                            "date": 1753632000
                        },
                        {
                            "amount": 6731,
                            "amount_med": 1157,
                            "date": 1754236800
                        },
                        {
                            "amount": 2461,
                            "amount_med": 1224,
                            "date": 1754841600
                        },
                        {
                            "amount": 3277,
                            "amount_med": 1158,
                            "date": 1755446400
                        },
                        {
                            "amount": 1856,
                            "amount_med": 1061,
                            "date": 1756051200
                        },
                        {
                            "amount": 2185,
                            "amount_med": 737,
                            "date": **********
                        }
                    ]
                },
                "new_attention_cnt": {
                    "amount": 3826,
                    "amount_pass_per": 0,
                    "amount_last": 0,
                    "amount_last_pass_per": 0,
                    "amount_change": -1246,
                    "amount_med": 1934,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 8704,
                            "amount_med": 3076,
                            "date": **********
                        },
                        {
                            "amount": 2999,
                            "amount_med": 3017,
                            "date": **********
                        },
                        {
                            "amount": 5570,
                            "amount_med": 3011,
                            "date": 1753632000
                        },
                        {
                            "amount": 8707,
                            "amount_med": 2895,
                            "date": 1754236800
                        },
                        {
                            "amount": 4135,
                            "amount_med": 2849,
                            "date": 1754841600
                        },
                        {
                            "amount": 6608,
                            "amount_med": 2748,
                            "date": 1755446400
                        },
                        {
                            "amount": 4371,
                            "amount_med": 2601,
                            "date": 1756051200
                        },
                        {
                            "amount": 3826,
                            "amount_med": 1934,
                            "date": **********
                        }
                    ]
                },
                "un_attention_cnt": {
                    "amount": 1641,
                    "amount_pass_per": 0,
                    "amount_last": 0,
                    "amount_last_pass_per": 0,
                    "amount_change": -3475,
                    "amount_med": 1115,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 2005,
                            "amount_med": 1645,
                            "date": **********
                        },
                        {
                            "amount": 1697,
                            "amount_med": 1673,
                            "date": **********
                        },
                        {
                            "amount": 1943,
                            "amount_med": 1707,
                            "date": 1753632000
                        },
                        {
                            "amount": 1976,
                            "amount_med": 1638,
                            "date": 1754236800
                        },
                        {
                            "amount": 1674,
                            "amount_med": 1602,
                            "date": 1754841600
                        },
                        {
                            "amount": 3331,
                            "amount_med": 1542,
                            "date": 1755446400
                        },
                        {
                            "amount": 2515,
                            "amount_med": 1451,
                            "date": 1756051200
                        },
                        {
                            "amount": 1641,
                            "amount_med": 1115,
                            "date": **********
                        }
                    ]
                },
                "play_trans_fan_per": {
                    "amount": 114,
                    "amount_pass_per": 8233,
                    "amount_last": 0,
                    "amount_last_pass_per": 0,
                    "amount_change": 5833,
                    "amount_med": 45,
                    "date": ********,
                    "tendency_list": [
                        {
                            "amount": 206,
                            "amount_med": 51,
                            "date": **********
                        },
                        {
                            "amount": 82,
                            "amount_med": 51,
                            "date": **********
                        },
                        {
                            "amount": 107,
                            "amount_med": 51,
                            "date": 1753632000
                        },
                        {
                            "amount": 62,
                            "amount_med": 51,
                            "date": 1754236800
                        },
                        {
                            "amount": 64,
                            "amount_med": 48,
                            "date": 1754841600
                        },
                        {
                            "amount": 61,
                            "amount_med": 49,
                            "date": 1755446400
                        },
                        {
                            "amount": 72,
                            "amount_med": 46,
                            "date": 1756051200
                        },
                        {
                            "amount": 114,
                            "amount_med": 45,
                            "date": **********
                        }
                    ]
                },
                "viewer_play": {
                    "amount": 221188,
                    "amount_pass_per": 0,
                    "amount_last": 0,
                    "amount_last_pass_per": 0,
                    "amount_change": -4410,
                    "amount_med": 213185,
                    "date": ********,
                    "tendency_list": null
                },
                "arch_fans_num_all": {
                    "amount": 2540,
                    "amount_pass_per": 0,
                    "amount_last": 0,
                    "amount_last_pass_per": 0,
                    "amount_change": 0,
                    "amount_med": 0,
                    "date": ********,
                    "tendency_list": null
                },
                "tip": "净增粉丝较上周提升17.7%，[[超过50%同类]]，表现较好，继续加油"
            }
        }
        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/attention_analyze"
        result = await self._fetch_with_retry(url=url, params={"period": "0"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            # Extract tip and remove it from data for normalized insertion
            tip = data.pop('tip', None)

            # Insert data using normalized structure
            await self._insert_normalized_metrics('attention_analyze', data, current_timestamp, current_datetime, tip)

        return result

    @pull_schedule(hour=3, minute=0, day_of_week="tue", priority=TaskPriority.CRITICAL)
    @use_task_cookie(task_type="creator")
    @retry_on_failure(max_retries=1)
    async def fetch_weekly_archive_analyze(self):
        """
        url: https://member.bilibili.com/x/web/data/v2/account_diagnose/archive_analyze?period=0&tmid=&t=*************

        args: cookie
            period = 0 7day

        returns: data.数据周报的稿件列表
        {
            "data": {
                "pub_arc_list": [
                    {
                        "date": **********,
                        "cnt": 6,
                        "archive_info": [
                            {
                                "pubtime": **********,
                                "title": "【星瞳 X lulu】中中中！中日合拍！《天天天国地獄国》",
                                "aid": ***************,
                                "bvid": "BV1nEuRzwEiq",
                                "cover": "http://i1.hdslb.com/bfs/archive/97653b4df381a04b36e91dedb738cd6a89a4b058.jpg",
                                "view": 251964,
                                "duration": 0
                            },
                            {
                                "pubtime": 1752868517,
                                "title": "【直播回放】【3D】瞳讯有约第二期！！！ 2025年07月18日19点场",
                                "aid": 114874856377752,
                                "bvid": "BV1f8umz6ENR",
                                "cover": "http://i0.hdslb.com/bfs/archive/0fceb70cc5acdc2492647a39e4df9c548525521f.jpg",
                                "view": 8889,
                                "duration": 0
                            },
                            {
                                "pubtime": 1752832473,
                                "title": "啊？主播在BW现场落网了？",
                                "aid": 114873614730655,
                                "bvid": "BV1Sxu2zWE99",
                                "cover": "http://i0.hdslb.com/bfs/archive/3685a18a644eded011920745bc3a4e79ae72fc94.jpg",
                                "view": 39705,
                                "duration": 0
                            },
                            {
                                "pubtime": 1752660146,
                                "title": "一 刀 瞳 死！关于我在BW现场看我推的星瞳这件事！《アイドル（Idol）》",
                                "aid": 114862323666747,
                                "bvid": "BV1dBu4zCEHz",
                                "cover": "http://i2.hdslb.com/bfs/archive/a72dfea442067733719f83118a86e4fa85ae3260.jpg",
                                "view": 51569,
                                "duration": 0
                            },
                            {
                                "pubtime": 1752608674,
                                "title": "【直播回放】【2D】先聊天再看电锯惊魂2！ 2025年07月15日19点场",
                                "aid": 114857793818451,
                                "bvid": "BV1XYunzXEjP",
                                "cover": "http://i1.hdslb.com/bfs/archive/e32c974993626f7e0595f650bf4a9be6f0ff7cf5.jpg",
                                "view": 2065,
                                "duration": 0
                            },
                            {
                                "pubtime": 1752487868,
                                "title": "【百万粉纪念】那不是黑历史，那是我的来时路",
                                "aid": 114851032665991,
                                "bvid": "BV15FuBzgEdZ",
                                "cover": "http://i2.hdslb.com/bfs/archive/e76623a9e66bb95392bc57806f4052a792142293.jpg",
                                "view": 125455,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": **********,
                        "cnt": 5,
                        "archive_info": [
                            {
                                "pubtime": 1753476528,
                                "title": "【直播回放】【2D】K歌王者！太享受啦！ 2025年07月25日19点场",
                                "aid": 114914400210454,
                                "bvid": "BV1Dib9zbEHW",
                                "cover": "http://i2.hdslb.com/bfs/archive/cf98d77ffa602bdb504b1ce4ee415a63679ae7ce.jpg",
                                "view": 1632,
                                "duration": 0
                            },
                            {
                                "pubtime": 1753353252,
                                "title": "⚠️成人向慎入！⚠️主播竟惨遭打皮鼓！",
                                "aid": 114907739588857,
                                "bvid": "BV1AVbyzWEYm",
                                "cover": "http://i2.hdslb.com/bfs/archive/6b3ff5f2c1b5d79bda56ac7cce85f93d1338947d.jpg",
                                "view": 191260,
                                "duration": 0
                            },
                            {
                                "pubtime": 1753524000,
                                "title": "抱歉，美女都是会发光的！",
                                "aid": 114902169553048,
                                "bvid": "BV12L8FzTEjT",
                                "cover": "http://i2.hdslb.com/bfs/archive/32d437de60c52cfd385a94cf3838cf7fa0b5f73e.jpg",
                                "view": 42640,
                                "duration": 0
                            },
                            {
                                "pubtime": 1753211471,
                                "title": "【直播回放】【2D】四周年了谈谈心吧~ 2025年07月22日19点场",
                                "aid": 114897505488648,
                                "bvid": "BV1vcgWzKE1X",
                                "cover": "http://i2.hdslb.com/bfs/archive/cf98d77ffa602bdb504b1ce4ee415a63679ae7ce.jpg",
                                "view": 2010,
                                "duration": 0
                            },
                            {
                                "pubtime": 1753092378,
                                "title": "【星瞳×lulu】温暖纯享，跟着我们去无人岛旅行",
                                "aid": 114890643606978,
                                "bvid": "BV1VxgVzuEXa",
                                "cover": "http://i2.hdslb.com/bfs/archive/525e548692a01b3637f0367351caf518cdd36c1d.jpg",
                                "view": 53435,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": 1753632000,
                        "cnt": 5,
                        "archive_info": [
                            {
                                "pubtime": 1754216040,
                                "title": "小心！猎心魔女的勾心把戏！",
                                "aid": 114964278806004,
                                "bvid": "BV1qkhgzpEJ7",
                                "cover": "http://i0.hdslb.com/bfs/archive/d2cfd4336bc43886b322692a5f0fbf4e00faed23.jpg",
                                "view": 45361,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754175250,
                                "title": "【直播回放】【3D】星瞳四周年纪念直播！！ 2025年08月02日19点场",
                                "aid": 114959732182785,
                                "bvid": "BV1YzhPzcEMw",
                                "cover": "http://i2.hdslb.com/bfs/archive/ebdbd26dfa872a67366e84811636cc072a6692e9.jpg",
                                "view": 3065,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754107200,
                                "title": "听说，就是你想挑战我？",
                                "aid": 114954816457339,
                                "bvid": "BV1BThxzaEPb",
                                "cover": "http://i0.hdslb.com/bfs/archive/79f5eb1230ae2e6bba3a090ca2c85bd665c5df0d.jpg",
                                "view": 86247,
                                "duration": 0
                            },
                            {
                                "pubtime": 1753956433,
                                "title": "让子弹飞丨星瞳新衣概念片",
                                "aid": 114947249933795,
                                "bvid": "BV1uU8CzeE5S",
                                "cover": "http://i1.hdslb.com/bfs/archive/96f501998897f8e32b8018d41aef058fb7c05c96.jpg",
                                "view": 335235,
                                "duration": 0
                            },
                            {
                                "pubtime": 1753821053,
                                "title": "【直播回放】【2D】谈心2.0 2025年07月29日19点场",
                                "aid": 114937133272552,
                                "bvid": "BV1tY8dzAEic",
                                "cover": "http://i2.hdslb.com/bfs/archive/cf98d77ffa602bdb504b1ce4ee415a63679ae7ce.jpg",
                                "view": 2219,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": 1754236800,
                        "cnt": 7,
                        "archive_info": [
                            {
                                "pubtime": 1754820898,
                                "title": "注意⚠️我才是这里的主人",
                                "aid": 115003923367648,
                                "bvid": "BV1FMbPzvEMt",
                                "cover": "http://i1.hdslb.com/bfs/archive/6c9dc5e0b32c9f5bf1386acd620b99d4fcfe40a5.jpg",
                                "view": 70360,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754685004,
                                "title": "【直播回放】【1D】在吗？我在小韩！ 2025年08月08日20点场",
                                "aid": 114993521493305,
                                "bvid": "BV1LHtpzyEqp",
                                "cover": "http://i1.hdslb.com/bfs/archive/e5c2b142849ac6583d08bc917b99346de401c261.jpg",
                                "view": 1665,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754647462,
                                "title": "领域展开 夕阳下的绝美魔法",
                                "aid": 114992548482296,
                                "bvid": "BV1hLtUzyEDu",
                                "cover": "http://i1.hdslb.com/bfs/archive/710329f820cf216d2614a8e9ce012ef2dbef14e2.jpg",
                                "view": 45146,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754429422,
                                "title": "【直播回放】【2D】四周年直播答谢~ 2025年08月05日19点场",
                                "aid": 114976576507836,
                                "bvid": "BV1Ad4RzmEyV",
                                "cover": "http://i1.hdslb.com/bfs/archive/e32c974993626f7e0595f650bf4a9be6f0ff7cf5.jpg",
                                "view": 1715,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754393976,
                                "title": "《虚环》你看了吗？PV彩蛋“官方”一手讲解",
                                "aid": 114975922196321,
                                "bvid": "BV1j5tnzrE1v",
                                "cover": "http://i0.hdslb.com/bfs/archive/44792704fc7344fd500f8549f8852eff2a89b117.jpg",
                                "view": 906494,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754345189,
                                "title": "【直播回放】【2D】虚环打赢了复活赛！ 2025年08月04日19点场",
                                "aid": 114971123912662,
                                "bvid": "BV1THt5zkE97",
                                "cover": "http://i0.hdslb.com/bfs/archive/4af803a6885230e736613eb2d8f8d67db859afa5.jpg",
                                "view": 4246,
                                "duration": 0
                            },
                            {
                                "pubtime": 1754301727,
                                "title": "“虚拟主播酒后实况”",
                                "aid": 114969899305958,
                                "bvid": "BV1rYtGz4Egu",
                                "cover": "http://i2.hdslb.com/bfs/archive/5825204e053069f4e654e6e39cf9e26f20f21620.jpg",
                                "view": 98262,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": 1754841600,
                        "cnt": 4,
                        "archive_info": [
                            {
                                "pubtime": 1755424898,
                                "title": "1！2！3！丢！【JUMP—BLACKPINK】",
                                "aid": 115043517597224,
                                "bvid": "BV1d6YCzSED7",
                                "cover": "http://i1.hdslb.com/bfs/archive/33cd8078272922064b22d481eddcc03e1d9b723f.jpg",
                                "view": 33249,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755356801,
                                "title": "【直播回放】【3D】夏日小作战！！！ 2025年08月16日19点场",
                                "aid": 115038887085635,
                                "bvid": "BV1A5Yqz5EmS",
                                "cover": "http://i0.hdslb.com/bfs/archive/58858583f9761bf174b9568549d1679cf064ab58.jpg",
                                "view": 2024,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755252176,
                                "title": "舞蹈基础，转场就不基础",
                                "aid": 115032192977032,
                                "bvid": "BV1y5bWzwE3f",
                                "cover": "http://i0.hdslb.com/bfs/archive/ad7ae4e52439cc332d8db1127e41fd757ebe3030.jpg",
                                "view": 59342,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755037064,
                                "title": "【直播回放】【2D】回娘家！QQ炫舞手游！ 2025年08月12日19点场",
                                "aid": 115016271464660,
                                "bvid": "BV1KstZz8EcR",
                                "cover": "http://i2.hdslb.com/bfs/archive/18078ae2ebd524a05b1fd1d56f7cacec629d2380.jpg",
                                "view": 1567,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": 1755446400,
                        "cnt": 7,
                        "archive_info": [
                            {
                                "pubtime": 1755871902,
                                "title": "【直播回放】【2D】配音综艺：魔瞳降世！！！ 2025年08月22日18点场",
                                "aid": 115072676465723,
                                "bvid": "BV1M7ebzPEJw",
                                "cover": "http://i1.hdslb.com/bfs/archive/5bf02265afa01b19d964d5403a420a2d91a20406.jpg",
                                "view": 1221,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755856800,
                                "title": "哟，躺着呢您嘞",
                                "aid": 115071334221172,
                                "bvid": "BV1WAetz9Efx",
                                "cover": "http://i0.hdslb.com/bfs/archive/11d7797af088d8ba2416b28f4ab7835085abd54b.jpg",
                                "view": 177094,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755770650,
                                "title": "暂 停 看 腿",
                                "aid": 115066166970554,
                                "bvid": "BV1zUYQz7ERU",
                                "cover": "http://i0.hdslb.com/bfs/archive/be31183b721eb8f9f9a030e05cf2b7f1daec35e1.jpg",
                                "view": 93921,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755705746,
                                "title": "【直播回放】【2D】和nanami一起生死狙击2！！ 2025年08月20日19点场",
                                "aid": 115061586724461,
                                "bvid": "BV162eGzuEcM",
                                "cover": "http://i2.hdslb.com/bfs/archive/a0d3ec458695e112ad0978d94c3707c8290f8fec.jpg",
                                "view": 1517,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755617853,
                                "title": "【直播回放】【2D】标题什么的应该没人在意吧！ 2025年08月19日19点场",
                                "aid": 115055983135839,
                                "bvid": "BV1WAeNzxEMF",
                                "cover": "http://i2.hdslb.com/bfs/archive/cf98d77ffa602bdb504b1ce4ee415a63679ae7ce.jpg",
                                "view": 1667,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755598044,
                                "title": "结婚太多次，被管家活活打断了双腿",
                                "aid": 115054858996373,
                                "bvid": "BV1QWeAzAE2G",
                                "cover": "http://i1.hdslb.com/bfs/archive/4111b205fad8e5093302447e05e81f08e97252e4.jpg",
                                "view": 40930,
                                "duration": 0
                            },
                            {
                                "pubtime": 1755515021,
                                "title": "洛天依！你的兵来了！！！【星瞳vlog】",
                                "aid": 115049406465535,
                                "bvid": "BV1dQY4zMEym",
                                "cover": "http://i1.hdslb.com/bfs/archive/963e37d4d7b05ebeb942a6db80a4e4bbcd491bd0.jpg",
                                "view": 65393,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": 1756051200,
                        "cnt": 5,
                        "archive_info": [
                            {
                                "pubtime": 1756548109,
                                "title": "“老师！她在教室跳出了自由感”",
                                "aid": 115117119311529,
                                "bvid": "BV17ShqzYE79",
                                "cover": "http://i1.hdslb.com/bfs/archive/7c3057d3bf36b9c5cdd87cb57fe0bb2c4d46a124.jpg",
                                "view": 26926,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756477910,
                                "title": "【直播回放】【3D】初恋，谈吗 2025年08月29日19点场",
                                "aid": 115112404845855,
                                "bvid": "BV1dshRzkEjP",
                                "cover": "http://i2.hdslb.com/bfs/archive/06e8ab6ce3a627045f69d0732766a2d7cf509503.jpg",
                                "view": 2091,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756442193,
                                "title": "“跟我回去十八岁”",
                                "aid": 115110190385184,
                                "bvid": "BV1dzhQziExe",
                                "cover": "http://i1.hdslb.com/bfs/archive/3d195d08dbbdff8cbcd032b997310534a9287304.jpg",
                                "view": 78098,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756202578,
                                "title": "我丢！这雷战太猛了！",
                                "aid": 115094470002618,
                                "bvid": "BV1zrvNzpEoA",
                                "cover": "http://i2.hdslb.com/bfs/archive/824994bdee4809a8b92b643958914c4338db69c9.jpg",
                                "view": 73412,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756116000,
                                "title": "冰 火 里 面 唱 k",
                                "aid": 115088295988321,
                                "bvid": "BV1iteRzHEsK",
                                "cover": "http://i0.hdslb.com/bfs/archive/2e493ce5a07020763339db858ba739a14a2cd3b2.jpg",
                                "view": 172808,
                                "duration": 0
                            }
                        ]
                    },
                    {
                        "date": **********,
                        "cnt": 6,
                        "archive_info": [
                            {
                                "pubtime": 1757239200,
                                "title": "周末进行曲！一起《美美Sunday》！",
                                "aid": 115161696374129,
                                "bvid": "BV1eMYVzcEDq",
                                "cover": "http://i0.hdslb.com/bfs/archive/947401a3ad7ddc907965acc98b0fe427a198562c.jpg",
                                "view": 37616,
                                "duration": 0
                            },
                            {
                                "pubtime": 1757134010,
                                "title": "【直播回放】【2D】来看家人们为我快乐向前冲！！！ 2025年09月06日10点场",
                                "aid": 115155438405515,
                                "bvid": "BV1ZYavzbESS",
                                "cover": "http://i2.hdslb.com/bfs/archive/cf98d77ffa602bdb504b1ce4ee415a63679ae7ce.jpg",
                                "view": 947,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756981960,
                                "title": "“铃声一响，初恋登场！”",
                                "aid": 115145556627353,
                                "bvid": "BV1Ayamz6EB1",
                                "cover": "http://i0.hdslb.com/bfs/archive/ca1719042f8792b7d21f4404cbfc316a28d0e7e9.jpg",
                                "view": 37049,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756826937,
                                "title": "【直播回放】【2D】我都直播了那还说啥了兄弟！！！ 2025年09月02日19点场",
                                "aid": 115135272323639,
                                "bvid": "BV1JjaKzUE2d",
                                "cover": "http://i1.hdslb.com/bfs/archive/e32c974993626f7e0595f650bf4a9be6f0ff7cf5.jpg",
                                "view": 1758,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756809474,
                                "title": "课后蹦跳！青春就该这样吧！",
                                "aid": 115134248782023,
                                "bvid": "BV17faPz3E5P",
                                "cover": "http://i1.hdslb.com/bfs/archive/bd65b418b663e024ca20dc52be817cf5bd636904.jpg",
                                "view": 53868,
                                "duration": 0
                            },
                            {
                                "pubtime": 1756721668,
                                "title": "一杯敬自己（开学已老实版",
                                "aid": ***************,
                                "bvid": "BV1NXaJzhEFD",
                                "cover": "http://i2.hdslb.com/bfs/archive/245122f5a242150042986c071f791db3b7e1aeff.jpg",
                                "view": 115943,
                                "duration": 0
                            }
                        ]
                    }
                ]
            }
        }
        """
        url = "https://member.bilibili.com/x/web/data/v2/account_diagnose/archive_analyze"
        result = await self._fetch_with_retry(url=url, params={"period": "0"})

        if result and result.get('code') == 0 and result.get('data'):
            data = result['data']
            current_timestamp = self.get_current_timestamp()
            current_datetime = self.get_current_datetime_midnight()

            # Store the entire pub_arc_list as JSON since it's a complex nested structure
            record = (
                self.uid,
                json.dumps(data.get('pub_arc_list', [])),
                current_timestamp,
                current_datetime
            )

            await self._insert_data(creator_sql.insert_weekly_archive_analyze_table_sql, [record], "weekly_archive_analyze_table")

        return result
#   ----------------------------------  Weekly  ---------------------------------------
