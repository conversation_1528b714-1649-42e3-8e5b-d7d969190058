"""
Base Server Class for VUPs Server

Provides common functionality for all server implementations including:
- Database operations with retry logic
- HTTP requests with retry mechanisms
- Cookie management and credential handling
- Common error handling patterns
- Data insertion utilities
"""

import asyncio
import asyncpg
import aiohttp
import re
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, Dict, Any, List, Union, Tuple
from bilibili_api import Credential

from vups.logger import logger
from vups_server.sql.db_pool import get_connection
from vups_server.base.cookie_manager import get_task_cookie, get_cookie_field


class BaseServer(ABC):
    """
    Abstract base class for all VUPs server implementations

    Provides common functionality for database operations, HTTP requests,
    cookie management, and error handling.
    """

    # Common constants
    SQL_FAIL_DELAY = 3
    HTTP_FAIL_DELAY = 5
    MAX_HTTP_RETRIES = 3
    MAX_SQL_RETRIES = 3

    def __init__(self, task_type: str, uid: Optional[str] = None, credential: Optional[Credential] = None, db_conn=None):
        """
        Initialize base server

        Args:
            task_type: Type of task ('user', 'creator', 'live')
            uid: User ID for the server
            credential: Optional credential object
            db_conn: Optional database connection
        """
        self.task_type = task_type
        self.uid = uid
        self.conn = db_conn
        self.db_pool = get_connection

        # Initialize credential
        if credential:
            self.credential = credential
        else:
            self.credential = self._get_task_credential()

        # Common HTTP headers
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

    def _get_task_credential(self) -> Optional[Credential]:
        """
        Get credential from task cookie with fallback to config

        Returns:
            Credential object or None if not available
        """
        try:
            # Try to get task cookie first
            cookie_str = get_task_cookie(self.task_type)
            if cookie_str:
                logger.info(f"Using task cookie for {self.task_type}")
                return self._parse_cookie_string_to_credential(cookie_str)
            else:
                logger.info(f"No task cookie found for {self.task_type}, using config fallback")
                return self._get_config_credential()

        except Exception as e:
            logger.error(f"Error getting {self.task_type} task credential: {e}")
            return self._get_config_credential()

    def _parse_cookie_string_to_credential(self, cookie_str: str) -> Optional[Credential]:
        """
        Parse cookie string to Credential object

        Args:
            cookie_str: Cookie string in format "key1=value1; key2=value2"

        Returns:
            Credential object or None
        """
        try:
            cookie_dict = {}
            for item in cookie_str.split('; '):
                if '=' in item:
                    key, value = item.split('=', 1)
                    cookie_dict[key] = value

            return Credential(
                sessdata=cookie_dict.get('SESSDATA', ''),
                bili_jct=cookie_dict.get('bili_jct', ''),
                buvid3=cookie_dict.get('buvid3', ''),
                dedeuserid=cookie_dict.get('DedeUserID', ''),
                buvid4=cookie_dict.get('buvid4', '')
            )
        except Exception as e:
            logger.error(f"Error parsing cookie string: {e}")
            return None

    def _get_config_credential(self) -> Optional[Credential]:
        """
        Get credential from config file as fallback

        Returns:
            Credential object or None
        """
        try:
            return Credential(
                sessdata=get_cookie_field(self.task_type, "SESSDATA"),
                bili_jct=get_cookie_field(self.task_type, "bili_jct"),
                buvid3=get_cookie_field(self.task_type, "buvid3"),
                dedeuserid=get_cookie_field(self.task_type, "DedeUserID"),
                buvid4=get_cookie_field(self.task_type, "buvid4"),
            )
        except Exception as e:
            logger.error(f"Error getting config credential for {self.task_type}: {e}")
            return None

    async def _execute_sql(self, sql: str, params=None, fetch_one=False, fetch_all=False,
                          executemany_params=None, max_retries=None) -> Optional[Any]:
        """
        Execute SQL with smart retry logic using connection pool

        Args:
            sql: SQL statement to execute
            params: Parameters for single SQL execution
            fetch_one: Whether to fetch a single row result
            fetch_all: Whether to fetch all results
            executemany_params: List of parameter tuples for executemany
            max_retries: Maximum number of retries

        Returns:
            Query results or None if execution fails
        """
        if max_retries is None:
            max_retries = self.MAX_SQL_RETRIES

        retriable_errors = (
            asyncpg.exceptions.PostgresConnectionError,
            asyncpg.exceptions.InterfaceError,
            asyncpg.exceptions.ConnectionDoesNotExistError,
        )

        retry_count = 0
        last_exception = None

        while retry_count <= max_retries:
            try:
                async with self.db_pool() as db_conn:
                    if executemany_params:
                        await db_conn.executemany(sql, executemany_params)
                        return None
                    else:
                        if fetch_one:
                            result = await db_conn.fetchrow(sql, *params if params else [])
                            return result
                        elif fetch_all:
                            result = await db_conn.fetch(sql, *params if params else [])
                            return result
                        else:
                            await db_conn.execute(sql, *params if params else [])
                            return None

            except retriable_errors as e:
                last_exception = e
                retry_count += 1
                if retry_count <= max_retries:
                    delay = self.SQL_FAIL_DELAY * (2 ** (retry_count - 1))
                    logger.warning(
                        f"Retriable SQL error (Attempt {retry_count}/{max_retries}): {e}\n"
                        f"SQL: {sql}\nRetrying in {delay} seconds"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Max retries ({max_retries}) reached for SQL execution: {e}")
                    return None

            except asyncpg.exceptions.DuplicateTableError as e:
                if re.match(r"^\s*CREATE\s+TABLE\s+IF\s+NOT\s+EXISTS", sql, re.IGNORECASE):
                    logger.warning(f"Table already exists (expected): {e}")
                    return None
                else:
                    logger.error(f"Unhandled DuplicateTableError: {e}")
                    return None

            except asyncpg.exceptions.PostgresError as e:
                logger.error(f"Non-retriable SQL error: {e}\nSQL: {sql}")
                return None

        if last_exception:
            logger.error(f"SQL execution failed after {max_retries} retries: {last_exception}")
        return None

    async def _fetch_with_retry(self, url: str, params=None, max_retries=None) -> Optional[Dict]:
        """
        Execute HTTP GET requests with smart retry logic

        Args:
            url: URL to fetch
            params: Dictionary of query parameters
            max_retries: Maximum number of retries

        Returns:
            JSON response data or None if fetching fails
        """
        if max_retries is None:
            max_retries = self.MAX_HTTP_RETRIES

        retry_count = 0
        last_exception = None

        while retry_count <= max_retries:
            try:
                async with aiohttp.ClientSession(headers=self.headers) as session:
                    cookies = self.credential.get_cookies() if self.credential else None

                    async with session.get(url, params=params, cookies=cookies) as response:
                        response.raise_for_status()
                        return await response.json()

            except aiohttp.ClientError as e:
                last_exception = e
                retry_count += 1
                if retry_count <= max_retries:
                    delay = self.HTTP_FAIL_DELAY * (2 ** (retry_count - 1))
                    logger.warning(
                        f"HTTP error (Attempt {retry_count}/{max_retries}): {e}\n"
                        f"URL: {url}\nRetrying in {delay} seconds"
                    )
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Max retries ({max_retries}) reached for HTTP fetch: {e}")
                    return None

            except Exception as e:
                logger.error(f"Unexpected HTTP error: {e}\nURL: {url}")
                return None

        if last_exception:
            logger.error(f"HTTP fetch failed after {max_retries} retries: {last_exception}")
        return None

    async def _insert_data(self, sql: str, data: Union[Tuple, List[Tuple]], table_name: str):
        """
        Generic data insertion method for single or bulk insertions

        Args:
            sql: Insert SQL statement
            data: Single tuple or list of tuples to insert
            table_name: Name of table for logging
        """
        if not data:
            logger.info(f"No data to insert into {table_name}")
            return

        if isinstance(data, list) and all(isinstance(d, tuple) for d in data):
            # Bulk insertion
            logger.info(f"Inserting {len(data)} records into {table_name}")
            await self._execute_sql(sql, executemany_params=data)
            logger.info(f"Successfully inserted {len(data)} records into {table_name}")
        elif isinstance(data, tuple):
            # Single insertion
            logger.info(f"Inserting 1 record into {table_name}")
            await self._execute_sql(sql, params=data)
            logger.info(f"Successfully inserted 1 record into {table_name}")
        else:
            logger.error(f"Invalid data format for {table_name}. Expected tuple or list of tuples")

    async def table_exists(self, table_name: str, schema_name: str = "public") -> bool:
        """
        Check if table exists in database

        Args:
            table_name: Name of the table to check
            schema_name: Schema name (default: public)

        Returns:
            True if table exists, False otherwise
        """
        full_table_name = f"{schema_name}.{table_name}"
        try:
            # Use standard PostgreSQL information_schema query
            sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = $1 AND table_name = $2
                )
            """
            result = await self._execute_sql(sql, params=(schema_name, table_name), fetch_one=True)
            return result[0] if result else False
        except Exception as e:
            logger.error(f"Error checking if table {full_table_name} exists: {e}")
            return False

    async def _create_tables(self):
        """Create database tables - to be implemented by subclasses"""
        pass

    def get_current_timestamp(self) -> int:
        """Get current timestamp in seconds"""
        return int(time.time())

    def get_current_datetime(self) -> datetime:
        """Get current datetime object"""
        return datetime.fromtimestamp(self.get_current_timestamp())

    def get_current_datetime_midnight(self) -> datetime:
        """Get current datetime at midnight (00:00:00)"""
        return self.get_current_datetime().replace(hour=0, minute=0, second=0, microsecond=0)

    async def refresh_credential(self) -> bool:
        """
        Refresh credential using cookie manager

        Returns:
            True if refresh successful, False otherwise
        """
        try:
            from vups_server.base.cookie_manager import get_cookie_manager
            cookie_manager = get_cookie_manager()

            success = await cookie_manager.refresh_cookie_for_task(self.task_type)
            if success:
                # Update credential with new cookie
                self.credential = self._get_task_credential()
                logger.info(f"Successfully refreshed credential for {self.task_type}")
                return True
            else:
                logger.warning(f"Failed to refresh credential for {self.task_type}")
                return False
        except Exception as e:
            logger.error(f"Error refreshing credential for {self.task_type}: {e}")
            return False

    def validate_credential(self) -> bool:
        """
        Validate that credential has required fields

        Returns:
            True if credential is valid, False otherwise
        """
        if not self.credential:
            return False

        # Check for essential fields
        cookies = self.credential.get_cookies()
        if not cookies.get('SESSDATA'):
            logger.error(f"Missing SESSDATA for {self.task_type}")
            return False

        return True

    @abstractmethod
    async def initialize_async(self):
        """Initialize server-specific async components"""
        pass

    @abstractmethod
    async def get_server_status(self) -> Dict[str, Any]:
        """Get current server status"""
        pass
