# VUPS Backend API

High-performance backend API framework using Go + Celery + Redis for VUPS (Virtual UP Streamer) data processing and analysis.

## Features

- **RESTful API**: Clean REST endpoints for VUP data access
- **Streaming Support**: Server-Sent Events (SSE) for real-time data streaming
- **Async Processing**: Celery integration for background task processing
- **Caching**: Redis-based caching and message queuing
- **Python Integration**: Seamless integration with existing Python modules
- **Middleware**: CORS, logging, rate limiting, and request ID tracking
- **Documentation**: OpenAPI/Swagger documentation

## API Endpoints

### 1. VUP Searcher Streaming Interface
- `POST /api/v1/vup/search/stream` - Streaming search with SSE support
- `POST /api/v1/vup/search` - Standard search endpoint

### 2. Live Info Data Query Interface
- `GET /api/v1/live/danmu` - Query danmu data by room and timespan
- `GET /api/v1/live/superchat` - Query superchat data
- `GET /api/v1/live/status` - Query live status information
- `GET /api/v1/live/analytics` - Query live analytics data

### 3. VUP User Data Query Interface
- `GET /api/v1/user/{uid}/stats` - Get user statistics
- `GET /api/v1/user/{uid}/content` - Get user content (videos, dynamics)
- `GET /api/v1/user/{uid}/analytics` - Get user analytics data
- `GET /api/v1/user/{uid}/info` - Get comprehensive user information

## Project Structure

```
vups_backend/
├── cmd/server/          # Application entry point
├── internal/
│   ├── api/            # API handlers and routes
│   ├── config/         # Configuration management
│   ├── models/         # Data models
│   ├── services/       # Business logic
│   └── utils/          # Utility functions
├── pkg/
│   ├── celery/         # Celery integration
│   ├── redis/          # Redis client
│   └── python/         # Python script execution
├── configs/            # Configuration files
└── docs/              # API documentation
```

## Getting Started

1. Install dependencies:
```bash
cd vups_backend
go mod tidy
```

2. Configure environment:
```bash
cp configs/config.example.yaml configs/config.yaml
# Edit configuration as needed
```

3. Run the server:
```bash
go run cmd/server/main.go
```

4. Access API documentation:
```
http://localhost:8080/swagger/index.html
```

## Configuration

Configuration is managed through YAML files and environment variables. See `configs/config.yaml` for available options.

## Dependencies

- **Gin**: HTTP web framework
- **Redis**: Caching and message queuing
- **Viper**: Configuration management
- **Logrus**: Structured logging
- **Swagger**: API documentation
