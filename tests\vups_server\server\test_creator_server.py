import asyncio
from vups_server.server.creator_info_server import CreatorInfoServer

async def test_creator_server():
    server = CreatorInfoServer()
    await server.initialize_async()
    # await server.fetch_key_data_overview()
    # await server.fetch_weekly_key_data_overview()
    # await server.fetch_weekly_play_analyze()
    # await server.fetch_weekly_attention_analyze()
    # await server.fetch_weekly_archive_analyze()

    await server.fetch_key_elec_num()
    await server.fetch_key_video_compare()

if __name__ == "__main__":
    asyncio.run(test_creator_server())
