package routes

import (
	"net/http"
	"time"

	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"vups_backend/internal/api/handlers"
	"vups_backend/internal/api/middleware"
	"vups_backend/internal/config"
	"vups_backend/internal/models"
	"vups_backend/internal/services"
)

// Router holds all the dependencies for routing
type Router struct {
	config             *config.Config
	logger             *logrus.Logger
	vupSearchHandler   *handlers.VUPSearchHandler
	liveInfoHandler    *handlers.LiveInfoHandler
	userDataHandler    *handlers.UserDataHandler
}

// NewRouter creates a new router instance
func NewRouter(
	config *config.Config,
	logger *logrus.Logger,
	vupSearchService *services.VUPSearchService,
	liveInfoService *services.LiveInfoService,
	userDataService *services.UserDataService,
) *Router {
	return &Router{
		config:             config,
		logger:             logger,
		vupSearchHandler:   handlers.NewVUPSearchHandler(vupSearchService),
		liveInfoHandler:    handlers.NewLiveInfoHandler(liveInfoService),
		userDataHandler:    handlers.NewUserDataHandler(userDataService),
	}
}

// SetupRoutes sets up all the routes
func (r *Router) SetupRoutes() *gin.Engine {
	// Set Gin mode
	gin.SetMode(r.config.Server.Mode)
	
	// Create Gin engine
	engine := gin.New()
	
	// Add global middleware
	r.setupGlobalMiddleware(engine)
	
	// Setup API routes
	r.setupAPIRoutes(engine)
	
	// Setup health check
	r.setupHealthCheck(engine)
	
	// Setup documentation
	r.setupDocumentation(engine)
	
	// Setup error handlers
	r.setupErrorHandlers(engine)
	
	return engine
}

// setupGlobalMiddleware sets up global middleware
func (r *Router) setupGlobalMiddleware(engine *gin.Engine) {
	// Request ID middleware
	engine.Use(requestid.New())
	
	// CORS middleware
	engine.Use(middleware.CORSMiddleware(&r.config.Server.CORS))
	
	// Logging middleware
	engine.Use(middleware.StructuredLoggingMiddleware(r.logger))
	
	// Error handling middleware
	engine.Use(middleware.ErrorHandlerMiddleware(r.logger))
	
	// Timeout middleware
	engine.Use(middleware.TimeoutMiddleware(r.logger))
	
	// Rate limiting middleware
	engine.Use(middleware.RateLimitMiddleware(&r.config.RateLimit))
	
	// Recovery middleware
	engine.Use(gin.Recovery())
}

// setupAPIRoutes sets up API routes
func (r *Router) setupAPIRoutes(engine *gin.Engine) {
	// API v1 group
	v1 := engine.Group("/api/v1")
	
	// VUP Search routes
	vupGroup := v1.Group("/vup")
	{
		vupGroup.POST("/search", r.vupSearchHandler.Search)
		vupGroup.POST("/search/async", r.vupSearchHandler.SearchAsync)
		vupGroup.POST("/search/stream", r.vupSearchHandler.SearchStream)
		vupGroup.GET("/search/task/:task_id", r.vupSearchHandler.GetTaskResult)
	}
	
	// Live Info routes
	liveGroup := v1.Group("/live")
	{
		liveGroup.GET("/danmu", r.liveInfoHandler.QueryDanmu)
		liveGroup.GET("/superchat", r.liveInfoHandler.QuerySuperchat)
		liveGroup.GET("/status", r.liveInfoHandler.QueryStatus)
		liveGroup.GET("/current", r.liveInfoHandler.QueryCurrentLiveInfo)
		liveGroup.GET("/analytics", r.liveInfoHandler.QueryAnalytics)
		liveGroup.POST("/async", r.liveInfoHandler.QueryLiveInfoAsync)
	}
	
	// User Data routes
	userGroup := v1.Group("/user")
	{
		userGroup.GET("/:uid/stats", r.userDataHandler.GetUserStats)
		userGroup.GET("/:uid/content", r.userDataHandler.GetUserContent)
		userGroup.GET("/:uid/analytics", r.userDataHandler.GetUserAnalytics)
		userGroup.GET("/:uid/info", r.userDataHandler.GetUserInfo)
		userGroup.GET("/:uid/period-stats", r.userDataHandler.GetUserPeriodStats)
		userGroup.POST("/async", r.userDataHandler.QueryUserDataAsync)
	}
}

// setupHealthCheck sets up health check endpoint
func (r *Router) setupHealthCheck(engine *gin.Engine) {
	engine.GET("/health", func(c *gin.Context) {
		health := models.HealthCheck{
			Status:    "healthy",
			Timestamp: time.Now(),
			Services: map[string]string{
				"api":    "healthy",
				"redis":  "healthy", // TODO: Add actual health checks
				"python": "healthy", // TODO: Add actual health checks
			},
		}
		
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    http.StatusOK,
			Message: "Service is healthy",
			Data:    health,
		})
	})
	
	// Readiness probe
	engine.GET("/ready", func(c *gin.Context) {
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    http.StatusOK,
			Message: "Service is ready",
		})
	})
	
	// Liveness probe
	engine.GET("/live", func(c *gin.Context) {
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    http.StatusOK,
			Message: "Service is alive",
		})
	})
}

// setupDocumentation sets up Swagger documentation
func (r *Router) setupDocumentation(engine *gin.Engine) {
	// Swagger documentation
	engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	
	// API documentation redirect
	engine.GET("/docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})
	
	// Root endpoint with API information
	engine.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, models.APIResponse{
			Code:    http.StatusOK,
			Message: "VUPS Backend API",
			Data: map[string]interface{}{
				"version":       "1.0.0",
				"description":   "High-performance backend API for VUPS data processing",
				"documentation": "/swagger/index.html",
				"health":        "/health",
				"endpoints": map[string]interface{}{
					"vup_search":  "/api/v1/vup/search",
					"live_info":   "/api/v1/live",
					"user_data":   "/api/v1/user",
				},
			},
		})
	})
}

// setupErrorHandlers sets up error handlers
func (r *Router) setupErrorHandlers(engine *gin.Engine) {
	// 404 handler
	engine.NoRoute(middleware.NotFoundHandler())
	
	// 405 handler
	engine.NoMethod(middleware.MethodNotAllowedHandler())
}
