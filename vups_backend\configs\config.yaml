server:
  host: "0.0.0.0"
  port: 9022
  mode: "release"  # debug, release, test
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  cors:
    allow_origins:
      - "*"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "*"
    expose_headers:
      - "Content-Length"
      - "Content-Type"
    allow_credentials: true
    max_age: "12h"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"
  idle_timeout: "300s"

celery:
  broker: "redis://localhost:6379/0"
  backend: "redis://localhost:6379/0"
  task_timeout: "300s"
  retry_delay: "5s"
  max_retries: 3

python:
  executable: "python"
  script_path: "../"
  timeout: "300s"
  max_workers: 5

logging:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "stdout"  # stdout, file
  filename: "logs/vups_backend.log"
  max_size: 100  # MB
  max_backups: 3
  max_age: 28  # days
  compress: true

rate_limit:
  enabled: true
  rate: 100.0  # requests per second
  burst: 200   # burst capacity
  window_size: "1m"
