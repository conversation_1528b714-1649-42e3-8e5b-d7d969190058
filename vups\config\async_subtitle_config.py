"""
Configuration for async subtitle generation system
"""
from dataclasses import dataclass
from typing import Optional
import os


@dataclass
class AsyncSubtitleConfig:
    """Configuration for async subtitle processing"""
    
    # Task management
    max_concurrent_tasks: int = 5
    task_cleanup_interval: int = 3600  # seconds (1 hour)
    max_task_age: int = 86400  # seconds (24 hours)
    
    # Progress reporting
    progress_update_interval: int = 10  # chunks between updates
    status_check_interval: int = 5  # seconds
    
    # Timeouts
    max_task_duration: int = 1800  # seconds (30 minutes)
    download_timeout: int = 300  # seconds (5 minutes)
    asr_timeout: int = 1200  # seconds (20 minutes)
    
    # File handling
    temp_dir: str = "temp/subtitle_tasks"
    cleanup_temp_files: bool = True
    max_file_size: int = 500 * 1024 * 1024  # 500MB
    
    # ASR settings
    default_asr_provider: str = "ali"  # "ali" or "bilibili"
    streaming_mode: bool = True
    chunk_size: int = 3200  # bytes
    sample_rate: int = 16000
    
    # Retry settings
    max_retries: int = 3
    retry_delay: int = 5  # seconds
    exponential_backoff: bool = True
    
    # Logging
    log_progress: bool = True
    log_level: str = "INFO"
    
    # Performance
    enable_chunked_processing: bool = True
    max_chunk_duration: int = 300  # seconds (5 minutes per chunk)
    parallel_chunk_processing: bool = False  # Future feature
    
    @classmethod
    def from_env(cls) -> 'AsyncSubtitleConfig':
        """Create config from environment variables"""
        return cls(
            max_concurrent_tasks=int(os.getenv('SUBTITLE_MAX_CONCURRENT_TASKS', '5')),
            task_cleanup_interval=int(os.getenv('SUBTITLE_CLEANUP_INTERVAL', '3600')),
            max_task_duration=int(os.getenv('SUBTITLE_MAX_DURATION', '1800')),
            default_asr_provider=os.getenv('SUBTITLE_ASR_PROVIDER', 'ali'),
            streaming_mode=os.getenv('SUBTITLE_STREAMING_MODE', 'true').lower() == 'true',
            log_level=os.getenv('SUBTITLE_LOG_LEVEL', 'INFO'),
            temp_dir=os.getenv('SUBTITLE_TEMP_DIR', 'temp/subtitle_tasks'),
        )


# Global config instance
config = AsyncSubtitleConfig.from_env()


def get_config() -> AsyncSubtitleConfig:
    """Get the global configuration instance"""
    return config


def update_config(**kwargs) -> None:
    """Update configuration values"""
    global config
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            raise ValueError(f"Unknown config parameter: {key}")
