package middleware

import (
	"net/http"
	"runtime/debug"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"vups_backend/internal/models"
)

// ErrorHandlerMiddleware creates an error handling middleware
func ErrorHandlerMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Log the panic
				logger.WithFields(logrus.Fields{
					"error": err,
					"stack": string(debug.Stack()),
					"path":  c.Request.URL.Path,
					"method": c.Request.Method,
				}).Error("Panic recovered")
				
				// Return error response
				c.JSON(http.StatusInternalServerError, models.ErrorResponse{
					Code:    http.StatusInternalServerError,
					Message: "Internal server error",
					Details: "An unexpected error occurred",
				})
				c.Abort()
			}
		}()
		
		c.Next()
		
		// Handle errors that were added to the context
		if len(c.<PERSON>rro<PERSON>) > 0 {
			err := c.Errors.Last()
			
			logger.WithFields(logrus.Fields{
				"error": err.Error(),
				"type":  err.Type,
				"path":  c.Request.URL.Path,
				"method": c.Request.Method,
			}).Error("Request error")
			
			// Don't override if response was already written
			if !c.Writer.Written() {
				switch err.Type {
				case gin.ErrorTypeBind:
					c.JSON(http.StatusBadRequest, models.ErrorResponse{
						Code:    http.StatusBadRequest,
						Message: "Invalid request format",
						Details: err.Error(),
					})
				case gin.ErrorTypePublic:
					c.JSON(http.StatusBadRequest, models.ErrorResponse{
						Code:    http.StatusBadRequest,
						Message: "Bad request",
						Details: err.Error(),
					})
				default:
					c.JSON(http.StatusInternalServerError, models.ErrorResponse{
						Code:    http.StatusInternalServerError,
						Message: "Internal server error",
						Details: "An error occurred while processing the request",
					})
				}
			}
		}
	}
}

// TimeoutMiddleware creates a timeout middleware
func TimeoutMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if request context is already cancelled
		select {
		case <-c.Request.Context().Done():
			logger.WithFields(logrus.Fields{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
			}).Warn("Request cancelled")
			
			c.JSON(http.StatusRequestTimeout, models.ErrorResponse{
				Code:    http.StatusRequestTimeout,
				Message: "Request timeout",
				Details: "The request took too long to process",
			})
			c.Abort()
			return
		default:
		}
		
		c.Next()
	}
}

// NotFoundHandler handles 404 errors
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Code:    http.StatusNotFound,
			Message: "Endpoint not found",
			Details: "The requested endpoint does not exist",
		})
	}
}

// MethodNotAllowedHandler handles 405 errors
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, models.ErrorResponse{
			Code:    http.StatusMethodNotAllowed,
			Message: "Method not allowed",
			Details: "The HTTP method is not allowed for this endpoint",
		})
	}
}
