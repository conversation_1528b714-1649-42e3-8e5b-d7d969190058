package services

import (
	"context"
	"fmt"
	"time"

	"vups_backend/internal/config"
	"vups_backend/internal/models"
	"vups_backend/pkg/celery"
	"vups_backend/pkg/python"
	"vups_backend/pkg/redis"
)

// UserDataService handles user data operations
type UserDataService struct {
	pythonExecutor *python.Executor
	redisClient    *redis.Client
	celeryClient   *celery.Client
	config         *config.Config
}

// NewUserDataService creates a new user data service
func NewUserDataService(
	pythonExecutor *python.Executor,
	redisClient *redis.Client,
	celeryClient *celery.Client,
	config *config.Config,
) *UserDataService {
	return &UserDataService{
		pythonExecutor: pythonExecutor,
		redisClient:    redisClient,
		celeryClient:   celeryClient,
		config:         config,
	}
}

// QueryUserData queries user data based on request parameters
func (s *UserDataService) QueryUserData(ctx context.Context, request *models.UserDataRequest) (interface{}, error) {
	// Check cache first
	cacheKey := s.redisClient.GenerateUserCacheKey(fmt.Sprintf("%s_%s_%s_%s",
		request.UID, request.StartTime, request.EndTime, request.DataType))

	var cachedResult interface{}
	if err := s.redisClient.Get(cacheKey, &cachedResult); err == nil {
		return cachedResult, nil
	}

	// Execute Python script
	result, err := s.pythonExecutor.UserDataQuery(ctx, request.UID, request.StartTime, request.EndTime, request.DataType)
	if err != nil {
		return nil, fmt.Errorf("failed to execute user data query: %w", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("user data query failed: %s", result.Error)
	}

	// Parse result based on data type
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid result format")
	}

	queryResult, ok := data["data"]
	if !ok {
		return nil, fmt.Errorf("invalid data format")
	}

	var response interface{}

	switch request.DataType {
	case "stats":
		response = s.parseUserStatsData(queryResult)
	case "content":
		response = s.parseUserContentData(queryResult)
	case "analytics":
		response = s.parseUserAnalyticsData(queryResult)
	case "info":
		response = s.parseUserInfoData(queryResult)
	case "period_stats":
		response = s.parsePeriodStatsData(queryResult)
	default:
		return nil, fmt.Errorf("unsupported data type: %s", request.DataType)
	}

	// Cache result for 5 minutes
	s.redisClient.Set(cacheKey, response, 5*time.Minute)

	return response, nil
}

// QueryUserDataAsync performs an async user data query using Celery
func (s *UserDataService) QueryUserDataAsync(ctx context.Context, request *models.UserDataRequest) (*models.TaskStatus, error) {
	// Send task to Celery
	task, err := s.celeryClient.SendUserDataQueryTask(request.UID, request.StartTime, request.EndTime, request.DataType)
	if err != nil {
		return nil, fmt.Errorf("failed to send async task: %w", err)
	}

	status := &models.TaskStatus{
		TaskID:    task.ID,
		Status:    "PENDING",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return status, nil
}

// parseUserStatsData parses user stats data from Python result
func (s *UserDataService) parseUserStatsData(data interface{}) *models.UserStatsData {
	if dataMap, ok := data.(map[string]interface{}); ok {
		stats := &models.UserStatsData{}

		if currentStat, ok := dataMap["current_stat"].(map[string]interface{}); ok {
			stats.FollowerCount = getInt(currentStat, "fan")
			stats.ViewCount = getInt(currentStat, "play")
			stats.LikeCount = getInt(currentStat, "like")
			stats.VideoCount = getInt(currentStat, "video")
		}

		if followerCount, ok := dataMap["follower_count"].(float64); ok {
			stats.FollowerCount = int(followerCount)
		}

		if dahanghaiCount, ok := dataMap["dahanghai_count"].(float64); ok {
			stats.DahanghaiCount = int(dahanghaiCount)
		}

		stats.LastUpdated = time.Now()
		return stats
	}
	return nil
}

// parseUserContentData parses user content data from Python result
func (s *UserDataService) parseUserContentData(data interface{}) *models.UserContentData {
	if dataMap, ok := data.(map[string]interface{}); ok {
		content := &models.UserContentData{}

		// Parse videos
		if videos, ok := dataMap["videos"].([]interface{}); ok {
			for _, video := range videos {
				if videoMap, ok := video.(map[string]interface{}); ok {
					videoData := models.VideoData{
						BVID:        getString(videoMap, "bvid"),
						Title:       getString(videoMap, "title"),
						Description: getString(videoMap, "description"),
						Duration:    getInt(videoMap, "duration"),
						ViewCount:   getInt(videoMap, "view"),
						LikeCount:   getInt(videoMap, "like"),
						CoinCount:   getInt(videoMap, "coin"),
						ShareCount:  getInt(videoMap, "share"),
					}
					content.Videos = append(content.Videos, videoData)
				}
			}
		}

		// Parse dynamics
		if dynamics, ok := dataMap["dynamics"].([]interface{}); ok {
			for _, dynamic := range dynamics {
				if dynamicMap, ok := dynamic.(map[string]interface{}); ok {
					dynamicData := models.DynamicData{
						DynamicID:  getString(dynamicMap, "dynamic_id"),
						Content:    getString(dynamicMap, "content"),
						Type:       getInt(dynamicMap, "type"),
						ViewCount:  getInt(dynamicMap, "view"),
						LikeCount:  getInt(dynamicMap, "like"),
						ShareCount: getInt(dynamicMap, "share"),
					}
					content.Dynamics = append(content.Dynamics, dynamicData)
				}
			}
		}

		return content
	}
	return nil
}

// parseUserAnalyticsData parses user analytics data from Python result
func (s *UserDataService) parseUserAnalyticsData(data interface{}) *models.UserAnalyticsData {
	if dataMap, ok := data.(map[string]interface{}); ok {
		analytics := &models.UserAnalyticsData{}

		// Parse top comments
		if comments, ok := dataMap["top_comments"].([]interface{}); ok {
			for _, comment := range comments {
				if commentMap, ok := comment.(map[string]interface{}); ok {
					commentData := models.CommentData{
						CommentID:  getString(commentMap, "comment_id"),
						Content:    getString(commentMap, "content"),
						LikeCount:  getInt(commentMap, "like"),
						ReplyCount: getInt(commentMap, "reply"),
						UserName:   getString(commentMap, "user_name"),
					}
					analytics.TopComments = append(analytics.TopComments, commentData)
				}
			}
		}

		// Parse top videos
		if videos, ok := dataMap["top_videos"].([]interface{}); ok {
			for _, video := range videos {
				if videoMap, ok := video.(map[string]interface{}); ok {
					videoData := models.VideoData{
						BVID:       getString(videoMap, "bvid"),
						Title:      getString(videoMap, "title"),
						ViewCount:  getInt(videoMap, "view"),
						LikeCount:  getInt(videoMap, "like"),
						CoinCount:  getInt(videoMap, "coin"),
						ShareCount: getInt(videoMap, "share"),
					}
					analytics.TopVideos = append(analytics.TopVideos, videoData)
				}
			}
		}

		return analytics
	}
	return nil
}

// parseUserInfoData parses user info data from Python result
func (s *UserDataService) parseUserInfoData(data interface{}) *models.UserInfoData {
	if dataMap, ok := data.(map[string]interface{}); ok {
		info := &models.UserInfoData{}

		if userInfo, ok := dataMap["user_info"].(map[string]interface{}); ok {
			info.Name = getString(userInfo, "name")
			info.Avatar = getString(userInfo, "face")
			info.Description = getString(userInfo, "sign")
			info.Level = getInt(userInfo, "level")
			info.VIPStatus = getInt(userInfo, "vip")
		}

		return info
	}
	return nil
}

// parsePeriodStatsData parses period stats data from Python result
func (s *UserDataService) parsePeriodStatsData(data interface{}) interface{} {
	return data
}
