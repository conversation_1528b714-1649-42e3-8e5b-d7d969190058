"""
Creator video query module.
Handles video-related data from key_video_compare_table and key_video_archive_table.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Union

import asyncpg
from vups.logger import logger
from vups_server.base.query_base import BaseQueryService


class CreatorVideoService(BaseQueryService):
    """Service for querying creator video comparison and archive data."""

    def __init__(self):
        super().__init__(cache_ttl=300)  # 5 minutes cache for historical data

    def _date_to_timestamp(self, date_input: Union[str, datetime]) -> int:
        """Convert date to Unix timestamp (bigint format used in database)."""
        if isinstance(date_input, str):
            try:
                dt = datetime.strptime(date_input, '%Y-%m-%d')
            except ValueError:
                logger.error(f"Invalid date format: {date_input}. Expected YYYY-MM-DD")
                return 0
        elif isinstance(date_input, datetime):
            dt = date_input
        else:
            logger.error(f"Invalid date type: {type(date_input)}")
            return 0
        
        return int(dt.timestamp())

    async def get_video_compare_data_by_uid_and_date(
        self,
        uid: str,
        date: Union[str, datetime]
    ) -> List[asyncpg.Record]:
        """
        Get video comparison data for a specific user and date.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object

        Returns:
            List of video comparison records
        """
        create_time = self._date_to_timestamp(date)
        if not create_time:
            return []

        cache_key = f"video_compare_{uid}_{create_time}"

        query = """
            SELECT uid, aid, bvid, title, cover, pubtime, duration, is_only_self,
                   play, vt, like_count, comment, dm, fav, coin, share, unfollow,
                   full_play_ratio, play_viewer_rate, play_fan_rate, active_fans_rate,
                   tm_rate, tm_pass_rate, crash_rate, interact_rate, avg_play_time,
                   total_new_attention_cnt, play_trans_fan_rate, tm_star,
                   crash_p50, crash_viewer_p50, crash_fan_p50, interact_p50,
                   interact_viewer_p50, interact_fan_p50, play_trans_fan_p50,
                   hour_play, hour_vt, hour_like, hour_comment, hour_dm, hour_fav,
                   hour_coin, hour_share, hour_tm_pass_rate, hour_interact_rate, hour_tm_star,
                   create_time, update_time
            FROM key_video_compare_table
            WHERE uid = $1 AND create_time = $2
            ORDER BY pubtime DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, create_time],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} video compare records for UID={uid}, date={date}")
        return results or []

    async def get_video_compare_data_by_date_range(
        self,
        uid: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime]
    ) -> List[asyncpg.Record]:
        """
        Get video comparison data for a date range.

        Args:
            uid: User UID
            start_date: Start date (inclusive)
            end_date: End date (inclusive)

        Returns:
            List of video comparison records
        """
        start_ts = self._date_to_timestamp(start_date)
        end_ts = self._date_to_timestamp(end_date)
        
        if not start_ts or not end_ts:
            return []

        cache_key = f"video_compare_range_{uid}_{start_ts}_{end_ts}"

        query = """
            SELECT uid, aid, bvid, title, cover, pubtime, duration, is_only_self,
                   play, vt, like_count, comment, dm, fav, coin, share, unfollow,
                   full_play_ratio, play_viewer_rate, play_fan_rate, active_fans_rate,
                   tm_rate, tm_pass_rate, crash_rate, interact_rate, avg_play_time,
                   total_new_attention_cnt, play_trans_fan_rate, tm_star,
                   crash_p50, crash_viewer_p50, crash_fan_p50, interact_p50,
                   interact_viewer_p50, interact_fan_p50, play_trans_fan_p50,
                   hour_play, hour_vt, hour_like, hour_comment, hour_dm, hour_fav,
                   hour_coin, hour_share, hour_tm_pass_rate, hour_interact_rate, hour_tm_star,
                   create_time, update_time
            FROM key_video_compare_table
            WHERE uid = $1 AND create_time >= $2 AND create_time <= $3
            ORDER BY create_time DESC, pubtime DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, start_ts, end_ts],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} video compare records for UID={uid}, range={start_date} to {end_date}")
        return results or []

    async def get_video_archive_data_by_uid(
        self,
        uid: str,
        limit: int = 50
    ) -> List[asyncpg.Record]:
        """
        Get video archive data for a specific user.

        Args:
            uid: User UID
            limit: Maximum number of records to return

        Returns:
            List of video archive records
        """
        cache_key = f"video_archive_{uid}_{limit}"

        query = """
            SELECT uid, aid, bvid, title, cover, ctime, pubtime, duration,
                   play_num, reply_num, likes_num, vt_num, fans_data,
                   full_play_ratio, is_only_self, create_time, update_time
            FROM key_video_archive_table
            WHERE uid = $1
            ORDER BY pubtime DESC
            LIMIT $2
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} video archive records for UID={uid}")
        return results or []

    async def get_video_archive_data_by_bvid(
        self,
        uid: str,
        bvid: str
    ) -> Optional[asyncpg.Record]:
        """
        Get specific video archive data by BVID.

        Args:
            uid: User UID
            bvid: Video BVID

        Returns:
            Video archive record or None if not found
        """
        cache_key = f"video_archive_bvid_{uid}_{bvid}"

        query = """
            SELECT uid, aid, bvid, title, cover, ctime, pubtime, duration,
                   play_num, reply_num, likes_num, vt_num, fans_data,
                   full_play_ratio, is_only_self, create_time, update_time
            FROM key_video_archive_table
            WHERE uid = $1 AND bvid = $2
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, bvid],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved video archive for UID={uid}, BVID={bvid}")
        else:
            logger.info(f"No video archive found for UID={uid}, BVID={bvid}")

        return result

    async def get_past_month_video_data(
        self,
        uid: str,
        months_ago: int = 1
    ) -> List[asyncpg.Record]:
        """
        Get video comparison data for the past month.

        Args:
            uid: User UID
            months_ago: Number of months ago (default: 1 for last month)

        Returns:
            List of video comparison records for the month
        """
        end_date = datetime.now() - timedelta(days=30 * months_ago)
        start_date = end_date - timedelta(days=29)  # 30 days total
        
        return await self.get_video_compare_data_by_date_range(uid, start_date, end_date)

    async def get_recent_video_performance(
        self,
        uid: str,
        days: int = 7
    ) -> List[asyncpg.Record]:
        """
        Get recent video performance data.

        Args:
            uid: User UID
            days: Number of recent days to query

        Returns:
            List of recent video comparison records
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return await self.get_video_compare_data_by_date_range(uid, start_date, end_date)

    async def get_video_elec_num_data(
        self,
        uid: str,
        limit: int = 10
    ) -> List[asyncpg.Record]:
        """
        Get electric number data for a user.

        Args:
            uid: User UID
            limit: Maximum number of records to return

        Returns:
            List of electric number records
        """
        cache_key = f"video_elec_num_{uid}_{limit}"

        query = """
            SELECT uid, all_total, month_total, custom_total, month_switch,
                   create_time, update_time
            FROM key_elec_num_table
            WHERE uid = $1
            ORDER BY update_time DESC
            LIMIT $2
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} electric number records for UID={uid}")
        return results or []

    async def get_latest_video_elec_num(self, uid: str) -> Optional[asyncpg.Record]:
        """
        Get the latest electric number data for a user.

        Args:
            uid: User UID

        Returns:
            Latest electric number record or None if not found
        """
        cache_key = f"latest_elec_num_{uid}"

        query = """
            SELECT uid, all_total, month_total, custom_total, month_switch,
                   create_time, update_time
            FROM key_elec_num_table
            WHERE uid = $1
            ORDER BY update_time DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved latest electric number for UID={uid}")
        else:
            logger.info(f"No electric number data found for UID={uid}")

        return result


# Global service instance
creator_video_service = CreatorVideoService()
