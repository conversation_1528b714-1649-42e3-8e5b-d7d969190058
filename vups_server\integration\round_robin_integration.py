"""
Round-Robin Integration Module

Integrates all refactored server components with the round-robin scheduler
and provides a unified interface for managing the VUPs server system.
"""

import asyncio
import signal
import sys
from datetime import datetime
from typing import Dict, Any, Optional, List

from vups.logger import logger
from vups_server.base.round_robin_scheduler import RoundRobinTaskScheduler, TaskDefinition, TaskPriority
from vups_server.base.task_decorators import get_task_registry, get_task_statistics
from vups_server.base.cookie_manager import get_cookie_manager
from vups_server.server.creator_info_server import CreatorInfoServer
from vups_server.server.live_info_till_server import LiveInfoServer, get_live_server
from vups_server.server.user_info_till_server import UserInfoServer


class VUPsRoundRobinSystem:
    """
    Main integration class for the VUPs Round-Robin System

    Manages all server instances, the round-robin scheduler, and provides
    a unified interface for the entire system.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}

        # Core components
        self.scheduler = RoundRobinTaskScheduler()
        self.cookie_manager = get_cookie_manager()
        self.task_registry = get_task_registry()

        # Server instances
        self.servers: Dict[str, Any] = {}

        # System state
        self.is_running = False
        self.startup_time: Optional[datetime] = None

        # Default configuration
        self.default_config = {
            'creator': {
                'uid': '401315430',
                'enabled': True
            },
            'user': {
                'char': 'xingtong',
                'enabled': True
            },
            'live': {
                'enabled': True
            }
        }

    async def initialize(self):
        """Initialize all system components"""
        logger.info("Initializing VUPs Round-Robin System...")

        try:
            # Initialize cookie manager
            await self.cookie_manager.initialize()
            logger.info("Cookie manager initialized")

            # Initialize server instances
            await self._initialize_servers()

            # Register servers with scheduler
            self._register_servers()

            # Register tasks from decorators
            self._register_decorated_tasks()

            logger.info("VUPs Round-Robin System initialization complete")

        except Exception as e:
            logger.error(f"Failed to initialize VUPs Round-Robin System: {e}")
            raise

    async def _initialize_servers(self):
        """Initialize all server instances"""
        config = {**self.default_config, **self.config}

        # Initialize Creator Info Server
        if config.get('creator', {}).get('enabled', True):
            try:
                creator_server = CreatorInfoServer(
                    uid=config['creator'].get('uid', '401315430')
                )
                await creator_server.initialize_async()
                self.servers['creator'] = creator_server
                logger.info("Creator Info Server initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Creator Info Server: {e}")

        # Initialize User Info Servers for all vtubers
        if config.get('user', {}).get('enabled', True):
            try:
                from vups.utils.bi_utils import get_vtuber_list
                vtuber_list = get_vtuber_list()
                logger.info(f"Initializing User Info Servers for {len(vtuber_list)} vtubers: {vtuber_list}")

                for vtuber in vtuber_list:
                    try:
                        user_server = UserInfoServer(char=vtuber)
                        await user_server.initialize_async()
                        server_id = f'user_{vtuber}'
                        self.servers[server_id] = user_server
                        logger.info(f"User Info Server initialized for {vtuber} (server_id: {server_id})")
                    except Exception as e:
                        logger.error(f"Failed to initialize User Info Server for {vtuber}: {e}")

            except Exception as e:
                logger.error(f"Failed to initialize User Info Servers: {e}")

        # Initialize Live Info Server
        if config.get('live', {}).get('enabled', True):
            try:
                live_server = get_live_server()
                await live_server.initialize_async()
                self.servers['live'] = live_server
                logger.info("Live Info Server initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Live Info Server: {e}")

    def _register_servers(self):
        """Register server instances with the scheduler"""
        for server_type, server_instance in self.servers.items():
            self.scheduler.register_server(server_type, server_instance)
            logger.info(f"Registered {server_type} server with scheduler")

    def _register_decorated_tasks(self):
        """Register tasks from decorated functions"""
        all_tasks = self.task_registry.get_all_tasks()
        total_registered = 0

        # Register tasks for each server instance
        for server_id, server_instance in self.servers.items():
            # For vtuber user servers, we need to register tasks with the specific server_id
            if server_id.startswith('user_'):
                vtuber_name = server_id[5:]  # Remove 'user_' prefix

                # Register tasks for this specific vtuber instance
                for task_name, task_def in all_tasks.items():
                    if task_def.server_type == 'user':
                        # Create a new task definition for this specific vtuber
                        vtuber_task_def = TaskDefinition(
                            name=f"{server_id}_{task_def.function_name}",
                            server_type=server_id,  # Use the specific server_id
                            function_name=task_def.function_name,
                            priority=task_def.priority,
                            frequency_minutes=task_def.frequency_minutes,
                            cron_schedule=task_def.cron_schedule,
                            max_execution_time=task_def.max_execution_time,
                            retry_count=task_def.retry_count,
                            retry_delay=task_def.retry_delay,
                            requires_cookie=task_def.requires_cookie,
                            dependencies=task_def.dependencies,
                            metadata=task_def.metadata
                        )
                        self.scheduler.add_task(vtuber_task_def)
                        logger.info(f"Registered task: {vtuber_task_def.name} for vtuber {vtuber_name}")
                        total_registered += 1
            else:
                # For non-user servers (creator, live), register tasks normally
                for task_name, task_def in all_tasks.items():
                    if task_def.server_type == server_id:
                        self.scheduler.add_task(task_def)
                        logger.info(f"Registered task: {task_name} for server {server_id}")
                        total_registered += 1

        logger.info(f"Registered {total_registered} tasks from decorators across all server instances")

    def add_custom_task(self, task_def: TaskDefinition):
        """Add a custom task to the scheduler"""
        self.scheduler.add_task(task_def)
        logger.info(f"Added custom task: {task_def.name}")

    async def start(self):
        """Start the round-robin system"""
        if self.is_running:
            logger.warning("System is already running")
            return

        try:
            logger.info("Starting VUPs Round-Robin System...")

            # Start the scheduler
            self.scheduler.start()

            # Mark as running
            self.is_running = True
            self.startup_time = datetime.now()

            logger.info("VUPs Round-Robin System started successfully")

        except Exception as e:
            logger.error(f"Failed to start VUPs Round-Robin System: {e}")
            raise

    async def stop(self):
        """Stop the round-robin system"""
        if not self.is_running:
            logger.warning("System is not running")
            return

        try:
            logger.info("Stopping VUPs Round-Robin System...")

            # Stop the scheduler
            self.scheduler.stop()

            # Close server sessions
            for server_type, server in self.servers.items():
                if hasattr(server, 'close_session'):
                    await server.close_session()
                    logger.info(f"Closed {server_type} server session")

            # Mark as stopped
            self.is_running = False

            logger.info("VUPs Round-Robin System stopped successfully")

        except Exception as e:
            logger.error(f"Error stopping VUPs Round-Robin System: {e}")
            raise

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        status = {
            'system': {
                'is_running': self.is_running,
                'startup_time': self.startup_time.isoformat() if self.startup_time else None,
                'uptime_seconds': (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0
            },
            'scheduler': self.scheduler.get_status(),
            'servers': {},
            'tasks': {
                'registered_count': len(self.task_registry.get_all_tasks()),
                'statistics': get_task_statistics()
            },
            'cookie_manager': self.cookie_manager.get_status()
        }

        # Get server statuses
        for server_type, server in self.servers.items():
            try:
                if hasattr(server, 'get_server_status'):
                    status['servers'][server_type] = asyncio.create_task(server.get_server_status())
                else:
                    status['servers'][server_type] = {'status': 'unknown'}
            except Exception as e:
                status['servers'][server_type] = {'status': 'error', 'error': str(e)}

        return status

    async def health_check(self) -> Dict[str, Any]:
        """Perform system health check"""
        health = {
            'overall_status': 'healthy',
            'checks': {},
            'timestamp': datetime.now().isoformat()
        }

        # Check scheduler
        try:
            scheduler_status = self.scheduler.get_status()
            health['checks']['scheduler'] = {
                'status': 'healthy' if scheduler_status['is_running'] else 'unhealthy',
                'details': scheduler_status
            }
        except Exception as e:
            health['checks']['scheduler'] = {
                'status': 'error',
                'error': str(e)
            }
            health['overall_status'] = 'unhealthy'

        # Check servers
        for server_type, server in self.servers.items():
            try:
                if hasattr(server, 'get_server_status'):
                    server_status = await server.get_server_status()
                    health['checks'][f'server_{server_type}'] = {
                        'status': 'healthy' if server_status.get('credential_valid', False) else 'warning',
                        'details': server_status
                    }
                else:
                    health['checks'][f'server_{server_type}'] = {
                        'status': 'unknown',
                        'details': 'No status method available'
                    }
            except Exception as e:
                health['checks'][f'server_{server_type}'] = {
                    'status': 'error',
                    'error': str(e)
                }
                health['overall_status'] = 'unhealthy'

        # Check cookie manager
        try:
            cookie_status = self.cookie_manager.get_status()
            enabled_tasks = cookie_status.get('enabled_tasks', [])
            health['checks']['cookie_manager'] = {
                'status': 'healthy' if enabled_tasks else 'warning',
                'details': cookie_status
            }
        except Exception as e:
            health['checks']['cookie_manager'] = {
                'status': 'error',
                'error': str(e)
            }
            health['overall_status'] = 'unhealthy'

        return health

    async def execute_manual_task(self, server_type: str, function_name: str, **kwargs) -> Dict[str, Any]:
        """Execute a task manually"""
        if server_type not in self.servers:
            return {
                'success': False,
                'error': f'Server type {server_type} not found'
            }

        server = self.servers[server_type]

        if not hasattr(server, function_name):
            return {
                'success': False,
                'error': f'Function {function_name} not found on {server_type} server'
            }

        try:
            start_time = datetime.now()
            function = getattr(server, function_name)
            result = await function(**kwargs)
            end_time = datetime.now()

            return {
                'success': True,
                'result': result,
                'execution_time': (end_time - start_time).total_seconds(),
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'execution_time': (datetime.now() - start_time).total_seconds()
            }


# Global system instance
_system_instance: Optional[VUPsRoundRobinSystem] = None


def get_system(config: Optional[Dict[str, Any]] = None) -> VUPsRoundRobinSystem:
    """Get or create the global system instance"""
    global _system_instance
    if _system_instance is None:
        _system_instance = VUPsRoundRobinSystem(config)
    return _system_instance


async def main():
    """Main entry point for running the system"""
    system = get_system()

    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        asyncio.create_task(system.stop())
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Initialize and start the system
        await system.initialize()
        await system.start()

        # Keep running
        logger.info("System is running. Press Ctrl+C to stop.")
        while system.is_running:
            await asyncio.sleep(1)

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"System error: {e}")
    finally:
        await system.stop()


if __name__ == "__main__":
    asyncio.run(main())
