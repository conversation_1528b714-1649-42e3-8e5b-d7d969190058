version: '3.8'

services:
  # VUPS Backend API
  vups-backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - VUPS_SERVER_HOST=0.0.0.0
      - VUPS_SERVER_PORT=8080
      - VUPS_REDIS_HOST=redis
      - VUPS_REDIS_PORT=6379
      - VUPS_PYTHON_EXECUTABLE=python3
    depends_on:
      - redis
      - celery-worker
    volumes:
      - ./logs:/app/logs
      - ./configs:/app/configs
    networks:
      - vups-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and message queuing
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./configs/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - vups-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker for async task processing
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.celery
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - PYTHONPATH=/app
    depends_on:
      - redis
    volumes:
      - ./scripts:/app/scripts
      - ./logs:/app/logs
    networks:
      - vups-network
    restart: unless-stopped
    deploy:
      replicas: 2

  # Celery flower for monitoring
  celery-flower:
    build:
      context: .
      dockerfile: Dockerfile.celery
    command: celery -A tasks flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
      - celery-worker
    networks:
      - vups-network
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./configs/nginx.conf:/etc/nginx/nginx.conf
      - ./configs/ssl:/etc/nginx/ssl
    depends_on:
      - vups-backend
    networks:
      - vups-network
    restart: unless-stopped

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./configs/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - vups-network
    restart: unless-stopped

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./configs/grafana:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - vups-network
    restart: unless-stopped

volumes:
  redis-data:
  prometheus-data:
  grafana-data:

networks:
  vups-network:
    driver: bridge
