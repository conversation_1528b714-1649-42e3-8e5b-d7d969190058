import time
from vups.algos.actions.video_analyse import VideoAnalyse
from vups.algos.tools.video_dump import video_source_downloader
from vups.logger import logger

text = """
[00:00:00 - 00:00:07] 第一杯增自己21年出道感受微生好时候，大家都说我长得丑，不想看我，后来最后看见了微圈也复火了。
[00:00:07 - 00:00:17] 22年上升期停播了，二三年上升期团队崩了，24年上升期嗓子坏了，25年花800万本演唱会涨粉，还比不过传武毕业的夜聊视频哦。
[00:00:17 - 00:00:43] 第二杯趁自己开始受击杀，小孩子不懂事说中文的，后来全世界都信了，出点啥事都是星瞳干的，做了个游戏小妹鬼，正规的人家说我们是游戏神学，第三辈子自己大家都不看好你咱们偏天要争气终于心头百万粉了循环明天也要工作了青舟已过万重山，乌蒙上连镇山外山啥也不说了，喝了这杯以后都是好日子了。
"""

async def test_download_subtitle(bvid: str = "BV1NXaJzhEFD", output_filename: str = "tests/vups/data/test_subtitle.txt") -> None:

    try:
        await video_source_downloader.download_subtitle(bvid=bvid, output_filename=output_filename, add_timestamp=True)

    except Exception as e:
        logger.error(f"Failed to download subtitle {bvid}: {e}")
        raise


async def model_test():
    va = VideoAnalyse()
    res = await va.run(text)

    print(res)

async def model_test2():
    time1 = time.time()
    va = VideoAnalyse()
    await test_download_subtitle(bvid = "BV1NXaJzhEFD")
    time2 = time.time()
    res = await va.run(text)

    print(res)
    print(f"Time cost: {time2 - time1} secs")

async def model_test3():
    time1 = time.time()
    va = VideoAnalyse()
    res = await va.video_analyse_with_bvid("BV1NXaJzhEFD")
    time2 = time.time()
    print(res)
    print(f"Time cost: {time2 - time1} secs")


if __name__ == '__main__':
    import asyncio
    asyncio.run(model_test3())
