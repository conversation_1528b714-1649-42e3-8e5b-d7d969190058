import asyncio
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    TRANSCRIBING = "transcribing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskProgress:
    """Task progress information"""
    status: TaskStatus
    progress_percent: float = 0.0
    current_step: str = ""
    estimated_remaining: Optional[int] = None  # seconds
    error_message: Optional[str] = None
    result: Optional[Any] = None
    created_at: datetime = None
    updated_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        self.updated_at = datetime.now()


class ProgressCallback:
    """Progress callback interface for ASR operations"""

    def __init__(self, task_id: str, task_manager: 'SubtitleTaskManager'):
        self.task_id = task_id
        self.task_manager = task_manager

    def update_progress(self, progress_percent: float, step: str, estimated_remaining: Optional[int] = None):
        """Update task progress"""
        self.task_manager.update_task_progress(
            self.task_id,
            progress_percent=progress_percent,
            current_step=step,
            estimated_remaining=estimated_remaining
        )

    def update_status(self, status: TaskStatus, error_message: Optional[str] = None):
        """Update task status"""
        self.task_manager.update_task_status(self.task_id, status, error_message)


class SubtitleTaskManager:
    """Manages background subtitle generation tasks"""

    def __init__(self):
        self.tasks: Dict[str, TaskProgress] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}

    def create_task(self, bvid: str, output_filename: str = None) -> str:
        """Create a new subtitle generation task"""
        task_id = str(uuid.uuid4())

        progress = TaskProgress(
            status=TaskStatus.PENDING,
            current_step="Task created, queued for processing"
        )

        self.tasks[task_id] = progress

        # Start background processing
        async_task = asyncio.create_task(
            self._process_subtitle_task(task_id, bvid, output_filename)
        )
        self.running_tasks[task_id] = async_task

        logger.info(f"Created subtitle task {task_id} for video {bvid}")
        return task_id

    def get_task_status(self, task_id: str) -> Optional[TaskProgress]:
        """Get current task status and progress"""
        return self.tasks.get(task_id)

    def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            self.update_task_status(task_id, TaskStatus.CANCELLED)
            return True
        return False

    def update_task_progress(self, task_id: str, progress_percent: float,
                           current_step: str, estimated_remaining: Optional[int] = None):
        """Update task progress"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.progress_percent = progress_percent
            task.current_step = current_step
            task.estimated_remaining = estimated_remaining
            task.updated_at = datetime.now()

    def update_task_status(self, task_id: str, status: TaskStatus, error_message: Optional[str] = None):
        """Update task status"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.status = status
            task.error_message = error_message
            task.updated_at = datetime.now()

    async def _process_subtitle_task(self, task_id: str, bvid: str, output_filename: str = None):
        """Background task processing"""
        try:
            from vups.algos.tools.video_dump import video_source_downloader

            # Create progress callback
            progress_callback = ProgressCallback(task_id, self)

            # Update status to downloading
            self.update_task_status(task_id, TaskStatus.DOWNLOADING)
            progress_callback.update_progress(10, "Starting video analysis...")

            # Process subtitle with progress tracking
            result = await self._download_subtitle_with_progress(
                bvid, output_filename, progress_callback
            )

            # Mark as completed
            task = self.tasks[task_id]
            task.result = result
            task.progress_percent = 100.0
            task.current_step = "Subtitle generation completed"
            self.update_task_status(task_id, TaskStatus.COMPLETED)

        except asyncio.CancelledError:
            self.update_task_status(task_id, TaskStatus.CANCELLED)
            logger.info(f"Task {task_id} was cancelled")
        except Exception as e:
            error_msg = f"Task failed: {str(e)}"
            self.update_task_status(task_id, TaskStatus.FAILED, error_msg)
            logger.error(f"Task {task_id} failed: {e}")
        finally:
            # Cleanup
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    async def _download_subtitle_with_progress(self, bvid: str, output_filename: str,
                                             progress_callback: ProgressCallback):
        """Download subtitle with progress tracking"""
        from vups.algos.tools.video_dump import VideoSourceDownloader
        from vups.algos.tools.asr import AliParaformerASR
        import os

        # Create enhanced video downloader with progress-aware ASR
        enhanced_asr = AliParaformerASR(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            streaming_mode=True,
            progress_callback=progress_callback
        )

        downloader = VideoSourceDownloader(asr="ali")
        # Replace the ASR instance with our progress-aware version
        downloader.asr = enhanced_asr

        progress_callback.update_progress(10, "Checking for existing subtitles...")

        try:
            result = await downloader.download_subtitle(
                bvid=bvid,
                output_filename=output_filename or "tests/vups/data/test_subtitle.txt"
            )
            return result
        except Exception as e:
            progress_callback.update_status(TaskStatus.FAILED, str(e))
            raise


# Global task manager instance
task_manager = SubtitleTaskManager()
