#!/usr/bin/env python3
"""
Live Info Query Wrapper Script
Wraps the existing live info query functionality for Go backend integration
"""

import sys
import json
import asyncio
from pathlib import Path

# Add the parent directory to Python path to import vups modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from vups_server.query.query_live_info_data import (
        query_danmu_by_room_and_timespan,
        query_superchat_by_room_and_timespan,
        query_live_status_by_room_and_time,
        query_now_live_info_by_room,
        query_whole_live_info_with_live_id,
        query_live_info_with_room_id
    )
    from vups.logger import logger
except ImportError as e:
    print(json.dumps({
        "success": False,
        "error": f"Failed to import vups modules: {str(e)}",
        "data": None
    }))
    sys.exit(1)


async def query_danmu_data(room_id, start_time, end_time):
    """Query danmu data"""
    try:
        # Convert time strings to timestamps if needed
        start_ts = start_time  # Assume already in correct format
        end_ts = end_time
        
        data, count = await query_danmu_by_room_and_timespan(room_id, start_ts, end_ts)
        return {
            "data": [dict(row) for row in data] if data else [],
            "count": count,
            "type": "danmu"
        }
    except Exception as e:
        raise Exception(f"Failed to query danmu data: {str(e)}")


async def query_superchat_data(room_id, start_time, end_time):
    """Query superchat data"""
    try:
        start_ts = start_time
        end_ts = end_time
        
        data, count = await query_superchat_by_room_and_timespan(room_id, start_ts, end_ts)
        return {
            "data": [dict(row) for row in data] if data else [],
            "count": count,
            "type": "superchat"
        }
    except Exception as e:
        raise Exception(f"Failed to query superchat data: {str(e)}")


async def query_live_status_data(room_id, timestamp):
    """Query live status data"""
    try:
        status = await query_live_status_by_room_and_time(room_id, timestamp)
        return {
            "status": status,
            "room_id": room_id,
            "timestamp": timestamp,
            "type": "live_status"
        }
    except Exception as e:
        raise Exception(f"Failed to query live status: {str(e)}")


async def query_current_live_info(room_id):
    """Query current live info"""
    try:
        info = await query_now_live_info_by_room(room_id)
        return {
            "data": dict(info) if info else None,
            "room_id": room_id,
            "type": "current_live_info"
        }
    except Exception as e:
        raise Exception(f"Failed to query current live info: {str(e)}")


async def query_live_analytics(room_id):
    """Query live analytics data"""
    try:
        analytics = await query_live_info_with_room_id(room_id)
        return {
            "data": [dict(row) for row in analytics] if analytics else [],
            "room_id": room_id,
            "type": "live_analytics"
        }
    except Exception as e:
        raise Exception(f"Failed to query live analytics: {str(e)}")


async def main():
    """Main function to execute live info queries"""
    if len(sys.argv) < 5:
        print(json.dumps({
            "success": False,
            "error": "Missing required arguments: room_id, start_time, end_time, data_type",
            "data": None
        }))
        sys.exit(1)
    
    room_id = sys.argv[1]
    start_time = sys.argv[2]
    end_time = sys.argv[3]
    data_type = sys.argv[4]
    
    try:
        result = None
        
        if data_type == "danmu":
            result = await query_danmu_data(room_id, start_time, end_time)
        elif data_type == "superchat":
            result = await query_superchat_data(room_id, start_time, end_time)
        elif data_type == "status":
            result = await query_live_status_data(room_id, start_time)
        elif data_type == "current":
            result = await query_current_live_info(room_id)
        elif data_type == "analytics":
            result = await query_live_analytics(room_id)
        else:
            raise Exception(f"Unsupported data type: {data_type}")
        
        # Return successful result
        print(json.dumps({
            "success": True,
            "data": result,
            "error": None
        }, ensure_ascii=False, default=str))
        
    except Exception as e:
        logger.error(f"Live info query error: {str(e)}")
        print(json.dumps({
            "success": False,
            "error": str(e),
            "data": None
        }))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
