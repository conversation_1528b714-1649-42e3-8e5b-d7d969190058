package celery

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"vups_backend/internal/config"
	redisClient "vups_backend/pkg/redis"
)

// Client represents a Celery client
type Client struct {
	redis  *redisClient.Client
	config *config.CeleryConfig
	ctx    context.Context
}

// Task represents a Celery task
type Task struct {
	ID      string                 `json:"id"`
	Task    string                 `json:"task"`
	Args    []interface{}          `json:"args"`
	Kwargs  map[string]interface{} `json:"kwargs"`
	Retries int                    `json:"retries"`
	ETA     *time.Time             `json:"eta,omitempty"`
	Expires *time.Time             `json:"expires,omitempty"`
}

// TaskResult represents a Celery task result
type TaskResult struct {
	TaskID    string      `json:"task_id"`
	Status    string      `json:"status"`
	Result    interface{} `json:"result"`
	Traceback string      `json:"traceback,omitempty"`
	Children  []string    `json:"children,omitempty"`
}

// TaskStatus represents possible task statuses
type TaskStatus string

const (
	StatusPending TaskStatus = "PENDING"
	StatusStarted TaskStatus = "STARTED"
	StatusSuccess TaskStatus = "SUCCESS"
	StatusFailure TaskStatus = "FAILURE"
	StatusRetry   TaskStatus = "RETRY"
	StatusRevoked TaskStatus = "REVOKED"
)

// NewClient creates a new Celery client
func NewClient(redisClient *redisClient.Client, cfg *config.CeleryConfig) *Client {
	return &Client{
		redis:  redisClient,
		config: cfg,
		ctx:    context.Background(),
	}
}

// SendTask sends a task to Celery
func (c *Client) SendTask(taskName string, args []interface{}, kwargs map[string]interface{}) (*Task, error) {
	taskID := generateTaskID()

	task := &Task{
		ID:     taskID,
		Task:   taskName,
		Args:   args,
		Kwargs: kwargs,
	}

	// Serialize task
	taskData, err := json.Marshal(task)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal task: %w", err)
	}

	// Send to Celery queue
	queueName := "celery"
	if err := c.redis.GetClient().LPush(c.ctx, queueName, taskData).Err(); err != nil {
		return nil, fmt.Errorf("failed to send task to queue: %w", err)
	}

	return task, nil
}

// GetTaskResult gets the result of a task
func (c *Client) GetTaskResult(taskID string) (*TaskResult, error) {
	resultKey := fmt.Sprintf("celery-task-meta-%s", taskID)

	var result TaskResult
	if err := c.redis.Get(resultKey, &result); err != nil {
		if err == redisClient.ErrKeyNotFound {
			return &TaskResult{
				TaskID: taskID,
				Status: string(StatusPending),
			}, nil
		}
		return nil, fmt.Errorf("failed to get task result: %w", err)
	}

	return &result, nil
}

// WaitForResult waits for a task to complete with timeout
func (c *Client) WaitForResult(taskID string, timeout time.Duration) (*TaskResult, error) {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		result, err := c.GetTaskResult(taskID)
		if err != nil {
			return nil, err
		}

		if result.Status != string(StatusPending) && result.Status != string(StatusStarted) {
			return result, nil
		}

		time.Sleep(100 * time.Millisecond)
	}

	return nil, fmt.Errorf("task %s timed out after %v", taskID, timeout)
}

// RevokeTask revokes a task
func (c *Client) RevokeTask(taskID string) error {
	revokeData := map[string]interface{}{
		"command": "revoke",
		"id":      taskID,
	}

	data, err := json.Marshal(revokeData)
	if err != nil {
		return fmt.Errorf("failed to marshal revoke command: %w", err)
	}

	return c.redis.GetClient().Publish(c.ctx, "celery.pidbox", data).Err()
}

// SendVUPSearchTask sends a VUP search task
func (c *Client) SendVUPSearchTask(question string) (*Task, error) {
	args := []interface{}{question}
	kwargs := map[string]interface{}{}

	return c.SendTask("vups.tasks.vup_search", args, kwargs)
}

// SendLiveInfoQueryTask sends a live info query task
func (c *Client) SendLiveInfoQueryTask(roomID, startTime, endTime, dataType string) (*Task, error) {
	args := []interface{}{roomID, startTime, endTime, dataType}
	kwargs := map[string]interface{}{}

	return c.SendTask("vups.tasks.live_info_query", args, kwargs)
}

// SendUserDataQueryTask sends a user data query task
func (c *Client) SendUserDataQueryTask(uid, startTime, endTime, dataType string) (*Task, error) {
	args := []interface{}{uid, startTime, endTime, dataType}
	kwargs := map[string]interface{}{}

	return c.SendTask("vups.tasks.user_data_query", args, kwargs)
}

// generateTaskID generates a unique task ID
func generateTaskID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}
