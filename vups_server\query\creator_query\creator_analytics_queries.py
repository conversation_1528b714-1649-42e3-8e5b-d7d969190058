"""
Creator analytics query module.
Provides comprehensive data collection functions for historical periods and aggregated analytics.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Union, Dict, Any

import asyncpg
from vups.logger import logger
from vups_server.base.query_base import BaseQueryService
from .creator_overview_queries import creator_overview_service
from .creator_weekly_queries import creator_weekly_service
from .creator_video_queries import creator_video_service


class CreatorAnalyticsService(BaseQueryService):
    """Service for comprehensive creator analytics and aggregated data collections."""

    def __init__(self):
        super().__init__(cache_ttl=600)  # 10 minutes cache for analytics data

    async def get_comprehensive_day_data(
        self,
        uid: str,
        date: Union[str, datetime]
    ) -> Dict[str, Any]:
        """
        Get comprehensive data collection for a specific past day.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object

        Returns:
            Dictionary containing all available data for the day
        """
        logger.info(f"Collecting comprehensive day data for UID={uid}, date={date}")
        
        # Get daily overview data
        overview_data = await creator_overview_service.get_daily_overview_by_uid_and_date(uid, date)
        
        # Get video data for the day
        video_data = await creator_video_service.get_video_compare_data_by_uid_and_date(uid, date)
        
        # Get electric number data (latest available)
        elec_data = await creator_video_service.get_latest_video_elec_num(uid)
        
        result = {
            "uid": uid,
            "date": str(date),
            "overview": dict(overview_data) if overview_data else None,
            "videos": [dict(record) for record in video_data] if video_data else [],
            "electric_numbers": dict(elec_data) if elec_data else None,
            "data_availability": {
                "has_overview": overview_data is not None,
                "video_count": len(video_data) if video_data else 0,
                "has_electric_data": elec_data is not None
            }
        }
        
        logger.info(f"Collected day data: overview={result['data_availability']['has_overview']}, "
                   f"videos={result['data_availability']['video_count']}")
        
        return result

    async def get_comprehensive_week_data(
        self,
        uid: str,
        week_start_date: Union[str, datetime]
    ) -> Dict[str, Any]:
        """
        Get comprehensive data collection for a specific past week.

        Args:
            uid: User UID
            week_start_date: Start date of the week

        Returns:
            Dictionary containing all available data for the week
        """
        logger.info(f"Collecting comprehensive week data for UID={uid}, week_start={week_start_date}")
        
        # Get weekly overview data
        overview_data = await creator_weekly_service.get_weekly_overview_data(uid, week_start_date)
        
        # Get weekly play analyze data
        play_analyze_data = await creator_weekly_service.get_weekly_play_analyze_data(uid, week_start_date)
        
        # Get weekly attention analyze data
        attention_analyze_data = await creator_weekly_service.get_weekly_attention_analyze_data(uid, week_start_date)
        
        # Get weekly archive analyze data
        archive_data = await creator_weekly_service.get_weekly_archive_analyze_data(uid)
        
        # Get daily overview data for the week (7 days)
        if isinstance(week_start_date, str):
            start_dt = datetime.strptime(week_start_date, '%Y-%m-%d')
        else:
            start_dt = week_start_date
        end_dt = start_dt + timedelta(days=6)
        
        daily_overview_data = await creator_overview_service.get_overview_data_by_date_range(
            uid, start_dt, end_dt
        )
        
        result = {
            "uid": uid,
            "week_start_date": str(week_start_date),
            "week_end_date": str(end_dt.date()),
            "weekly_overview": dict(overview_data) if overview_data else None,
            "weekly_play_analyze": dict(play_analyze_data) if play_analyze_data else None,
            "weekly_attention_analyze": dict(attention_analyze_data) if attention_analyze_data else None,
            "weekly_archives": [dict(record) for record in archive_data] if archive_data else [],
            "daily_overview_data": [dict(record) for record in daily_overview_data] if daily_overview_data else [],
            "data_availability": {
                "has_weekly_overview": overview_data is not None,
                "has_play_analyze": play_analyze_data is not None,
                "has_attention_analyze": attention_analyze_data is not None,
                "archive_count": len(archive_data) if archive_data else 0,
                "daily_data_count": len(daily_overview_data) if daily_overview_data else 0
            }
        }
        
        logger.info(f"Collected week data: overview={result['data_availability']['has_weekly_overview']}, "
                   f"daily_count={result['data_availability']['daily_data_count']}")
        
        return result

    async def get_comprehensive_month_data(
        self,
        uid: str,
        month_start_date: Union[str, datetime]
    ) -> Dict[str, Any]:
        """
        Get comprehensive data collection for a specific past month.

        Args:
            uid: User UID
            month_start_date: Start date of the month

        Returns:
            Dictionary containing all available data for the month
        """
        logger.info(f"Collecting comprehensive month data for UID={uid}, month_start={month_start_date}")
        
        # Calculate month date range (30 days)
        if isinstance(month_start_date, str):
            start_dt = datetime.strptime(month_start_date, '%Y-%m-%d')
        else:
            start_dt = month_start_date
        end_dt = start_dt + timedelta(days=29)  # 30 days total
        
        # Get daily overview data for the month
        daily_overview_data = await creator_overview_service.get_overview_data_by_date_range(
            uid, start_dt, end_dt
        )
        
        # Get video data for the month
        video_data = await creator_video_service.get_video_compare_data_by_date_range(
            uid, start_dt, end_dt
        )
        
        # Get weekly data for the month (multiple weeks)
        weekly_data = await creator_weekly_service.get_weekly_data_by_date_range(
            uid, start_dt, end_dt, "overview"
        )
        
        # Get video archive data
        video_archive_data = await creator_video_service.get_video_archive_data_by_uid(uid, 100)
        
        # Calculate aggregated statistics
        aggregated_stats = self._calculate_month_aggregations(daily_overview_data)
        
        result = {
            "uid": uid,
            "month_start_date": str(start_dt.date()),
            "month_end_date": str(end_dt.date()),
            "daily_overview_data": [dict(record) for record in daily_overview_data] if daily_overview_data else [],
            "video_compare_data": [dict(record) for record in video_data] if video_data else [],
            "weekly_metrics_data": [dict(record) for record in weekly_data] if weekly_data else [],
            "video_archive_data": [dict(record) for record in video_archive_data] if video_archive_data else [],
            "aggregated_statistics": aggregated_stats,
            "data_availability": {
                "daily_data_count": len(daily_overview_data) if daily_overview_data else 0,
                "video_compare_count": len(video_data) if video_data else 0,
                "weekly_metrics_count": len(weekly_data) if weekly_data else 0,
                "video_archive_count": len(video_archive_data) if video_archive_data else 0
            }
        }
        
        logger.info(f"Collected month data: daily_count={result['data_availability']['daily_data_count']}, "
                   f"video_count={result['data_availability']['video_compare_count']}")
        
        return result

    def _calculate_month_aggregations(self, daily_data: List[asyncpg.Record]) -> Dict[str, Any]:
        """Calculate aggregated statistics from daily data."""
        if not daily_data:
            return {}
        
        # Convert records to dicts for easier processing
        data_dicts = [dict(record) for record in daily_data]
        
        # Calculate aggregations
        total_play = sum(record.get('play', 0) or 0 for record in data_dicts)
        total_visitor = sum(record.get('visitor', 0) or 0 for record in data_dicts)
        total_fan = sum(record.get('fan', 0) or 0 for record in data_dicts)
        total_like = sum(record.get('like_count', 0) or 0 for record in data_dicts)
        
        # Calculate averages
        data_count = len(data_dicts)
        avg_play = total_play / data_count if data_count > 0 else 0
        avg_visitor = total_visitor / data_count if data_count > 0 else 0
        avg_fan = total_fan / data_count if data_count > 0 else 0
        avg_like = total_like / data_count if data_count > 0 else 0
        
        # Get first and last values for growth calculation
        first_record = data_dicts[0] if data_dicts else {}
        last_record = data_dicts[-1] if data_dicts else {}
        
        fan_growth = (last_record.get('fan', 0) or 0) - (first_record.get('fan', 0) or 0)
        play_growth = (last_record.get('play', 0) or 0) - (first_record.get('play', 0) or 0)
        
        return {
            "period_days": data_count,
            "totals": {
                "total_play": total_play,
                "total_visitor": total_visitor,
                "total_fan": total_fan,
                "total_like": total_like
            },
            "averages": {
                "avg_play_per_day": round(avg_play, 2),
                "avg_visitor_per_day": round(avg_visitor, 2),
                "avg_fan_per_day": round(avg_fan, 2),
                "avg_like_per_day": round(avg_like, 2)
            },
            "growth": {
                "fan_growth": fan_growth,
                "play_growth": play_growth,
                "fan_growth_rate": round((fan_growth / (first_record.get('fan', 1) or 1)) * 100, 2),
                "play_growth_rate": round((play_growth / (first_record.get('play', 1) or 1)) * 100, 2)
            }
        }

    async def get_data_by_configurable_range(
        self,
        uid: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        include_video_data: bool = True,
        include_weekly_data: bool = True
    ) -> Dict[str, Any]:
        """
        Get data for a configurable date range with optional data types.

        Args:
            uid: User UID
            start_date: Start date (inclusive)
            end_date: End date (inclusive)
            include_video_data: Whether to include video comparison data
            include_weekly_data: Whether to include weekly metrics data

        Returns:
            Dictionary containing requested data for the date range
        """
        logger.info(f"Collecting configurable range data for UID={uid}, "
                   f"range={start_date} to {end_date}")
        
        # Get daily overview data
        daily_data = await creator_overview_service.get_overview_data_by_date_range(
            uid, start_date, end_date
        )
        
        result = {
            "uid": uid,
            "start_date": str(start_date),
            "end_date": str(end_date),
            "daily_overview_data": [dict(record) for record in daily_data] if daily_data else []
        }
        
        # Optionally include video data
        if include_video_data:
            video_data = await creator_video_service.get_video_compare_data_by_date_range(
                uid, start_date, end_date
            )
            result["video_compare_data"] = [dict(record) for record in video_data] if video_data else []
        
        # Optionally include weekly data
        if include_weekly_data:
            weekly_data = await creator_weekly_service.get_weekly_data_by_date_range(
                uid, start_date, end_date, "overview"
            )
            result["weekly_metrics_data"] = [dict(record) for record in weekly_data] if weekly_data else []
        
        # Calculate aggregations
        result["aggregated_statistics"] = self._calculate_month_aggregations(daily_data)
        
        # Data availability summary
        result["data_availability"] = {
            "daily_data_count": len(daily_data) if daily_data else 0,
            "video_data_included": include_video_data,
            "weekly_data_included": include_weekly_data
        }
        
        if include_video_data:
            result["data_availability"]["video_compare_count"] = len(result.get("video_compare_data", []))
        
        if include_weekly_data:
            result["data_availability"]["weekly_metrics_count"] = len(result.get("weekly_metrics_data", []))
        
        logger.info(f"Collected configurable range data: "
                   f"daily_count={result['data_availability']['daily_data_count']}")
        
        return result


# Global service instance
creator_analytics_service = CreatorAnalyticsService()
