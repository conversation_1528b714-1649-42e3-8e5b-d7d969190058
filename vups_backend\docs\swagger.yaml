openapi: 3.0.0
info:
  title: VUPS Backend API
  description: High-performance backend API framework for VUPS data processing and analysis
  version: 1.0.0
  contact:
    name: API Support
    url: http://www.swagger.io/support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Development server
  - url: https://api.vups.example.com/api/v1
    description: Production server

paths:
  /vup/search:
    post:
      tags:
        - VUP Search
      summary: Search VUP data
      description: Perform a VUP search query
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VUPSearchRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VUPSearchResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /vup/search/stream:
    post:
      tags:
        - VUP Search
      summary: Stream VUP search
      description: Perform a VUP search query with streaming response
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VUPSearchRequest'
      responses:
        '200':
          description: Server-Sent Events stream
          content:
            text/event-stream:
              schema:
                type: string

  /vup/search/async:
    post:
      tags:
        - VUP Search
      summary: Async VUP search
      description: Perform an async VUP search query using Celery
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VUPSearchRequest'
      responses:
        '202':
          description: Task started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskResponse'

  /live/danmu:
    get:
      tags:
        - Live Info
      summary: Query danmu data
      description: Query danmu data by room ID and time range
      parameters:
        - name: room_id
          in: query
          required: true
          schema:
            type: string
          example: "22886883"
        - name: start_time
          in: query
          required: true
          schema:
            type: string
          example: "2024-01-01"
        - name: end_time
          in: query
          required: true
          schema:
            type: string
          example: "2024-01-31"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LiveInfoResponse'

  /user/{uid}/stats:
    get:
      tags:
        - User Data
      summary: Get user statistics
      description: Get user statistics by UID
      parameters:
        - name: uid
          in: path
          required: true
          schema:
            type: string
          example: "401315430"
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserStatsResponse'

components:
  schemas:
    VUPSearchRequest:
      type: object
      required:
        - question
      properties:
        question:
          type: string
          description: The search question
          example: "查询星瞳的最新数据"
        stream:
          type: boolean
          description: Whether to use streaming response
          example: false

    VUPSearchResponse:
      type: object
      properties:
        answer:
          type: string
          description: The search result
          example: "根据最新数据，星瞳的粉丝数为..."
        task_id:
          type: string
          description: Task ID for async requests
          example: "task_123456"

    TaskResponse:
      type: object
      properties:
        task_id:
          type: string
          description: The task ID
          example: "task_123456"
        status:
          type: string
          description: The task status
          example: "PENDING"

    LiveInfoResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
        count:
          type: integer
          description: Total count of records
        type:
          type: string
          description: Data type
          example: "danmu"

    UserStatsResponse:
      type: object
      properties:
        uid:
          type: string
          example: "401315430"
        follower_count:
          type: integer
          example: 100000
        dahanghai_count:
          type: integer
          example: 500
        view_count:
          type: integer
          example: 1000000
        like_count:
          type: integer
          example: 50000

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: Error code
          example: 400
        message:
          type: string
          description: Error message
          example: "Bad request"
        details:
          type: string
          description: Error details
          example: "Invalid request format"

    APIResponse:
      type: object
      properties:
        code:
          type: integer
          description: Response code
          example: 200
        message:
          type: string
          description: Response message
          example: "Success"
        data:
          type: object
          description: Response data

  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
    BasicAuth:
      type: http
      scheme: basic
