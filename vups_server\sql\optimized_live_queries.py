"""
Optimized SQL query templates for live streaming data.
These templates replace the inefficient queries in query_live_info_data.py
"""

# ============================================================================
# DANMU QUERIES - Optimized versions
# ============================================================================

DANMU_BY_TIMESPAN_QUERY = """
    SELECT id, room_id, live_id, user_id, user_name, message,
           medal_level, medal_name, timestamp, datetime
    FROM "{table_name}"
    WHERE timestamp BETWEEN $1 AND $2
    ORDER BY timestamp
"""

DANMU_BY_DATETIME_QUERY = """
    SELECT id, room_id, live_id, user_id, user_name, message,
           medal_level, medal_name, timestamp, datetime
    FROM "{table_name}"
    WHERE datetime BETWEEN $1 AND $2
    ORDER BY datetime
"""

# ============================================================================
# GIFT AND SUPERCHAT QUERIES - Optimized versions
# ============================================================================

SUPERCHAT_BY_TIMESPAN_QUERY = """
    SELECT id, room_id, live_id, user_id, user_name, price,
           super_chat_msg, gift_num, start_timestamp, datetime
    FROM super_chat_table
    WHERE room_id = $1 AND start_timestamp BETWEEN $2 AND $3
    ORDER BY start_timestamp
"""

SUPERCHAT_BY_DATETIME_QUERY = """
    SELECT id, room_id, live_id, user_id, user_name, price,
           super_chat_msg, gift_num, start_timestamp, datetime
    FROM super_chat_table
    WHERE room_id = $1 AND datetime BETWEEN $2 AND $3
    ORDER BY datetime
"""

GIFT_BY_TIMESPAN_QUERY = """
    SELECT id, room_id, live_id, user_id, user_name, gift_name,
           gift_num, gift_per_price, total_coin, timestamp, datetime
    FROM gift_table
    WHERE room_id = $1 AND timestamp BETWEEN $2 AND $3
    ORDER BY timestamp
"""

GIFT_BY_DATETIME_QUERY = """
    SELECT id, room_id, live_id, user_id, user_name, gift_name,
           gift_num, gift_per_price, total_coin, timestamp, datetime
    FROM gift_table
    WHERE room_id = $1 AND datetime BETWEEN $2 AND $3
    ORDER BY datetime
"""

# ============================================================================
# COUNT QUERIES - Optimized versions with specific aggregations
# ============================================================================

COUNT_BY_ROOM_AND_TIME_QUERY = """
    SELECT COALESCE(SUM(count), 0) as total_count
    FROM {table_name}
    WHERE room_id = $1 AND timestamp = $2
"""

COUNT_BY_ROOM_AND_DATETIME_RANGE_QUERY = """
    SELECT COALESCE({aggregation}(count), 0) as result
    FROM {table_name}
    WHERE room_id = $1 AND datetime BETWEEN $2 AND $3
"""

# ============================================================================
# LIVE STATUS QUERIES - Optimized versions
# ============================================================================

LIVE_STATUS_BY_ROOM_AND_TIME_QUERY = """
    SELECT live_status
    FROM live_status_minute_table
    WHERE room_id = $1 AND timestamp = $2
    LIMIT 1
"""

# Optimized version - eliminates subquery
CURRENT_LIVE_INFO_BY_ROOM_QUERY = """
    SELECT id, room_id, live_id, live_status, live_action, title,
           cover, parent_area, area, timestamp, datetime
    FROM live_status_minute_table
    WHERE room_id = $1
    ORDER BY timestamp DESC
    LIMIT 1
"""

# ============================================================================
# PAYMENT QUERIES - Optimized for multiple tables
# ============================================================================

PAYMENT_DATA_BY_DATE_QUERY = """
    SELECT 'gift_table' as table_type,
           total_coin, gift_per_price, gift_num, datetime
    FROM gift_table
    WHERE room_id = $1
      AND datetime >= $2::DATE
      AND datetime < $2::DATE + INTERVAL '1 DAY'

    UNION ALL

    SELECT 'buy_guard_table' as table_type,
           0 as total_coin, gift_per_price, gift_num, datetime
    FROM buy_guard_table
    WHERE room_id = $1
      AND datetime >= $2::DATE
      AND datetime < $2::DATE + INTERVAL '1 DAY'

    UNION ALL

    SELECT 'super_chat_table' as table_type,
           0 as total_coin, price as gift_per_price, gift_num, datetime
    FROM super_chat_table
    WHERE room_id = $1
      AND datetime >= $2::DATE
      AND datetime < $2::DATE + INTERVAL '1 DAY'
"""

PAYMENT_DATA_BY_TIME_RANGE_QUERY = """
    SELECT 'gift_table' as table_type,
           total_coin, gift_per_price, gift_num, {time_field}
    FROM gift_table
    WHERE room_id = $1 AND {time_field} BETWEEN $2 AND $3

    UNION ALL

    SELECT 'buy_guard_table' as table_type,
           0 as total_coin, gift_per_price, gift_num, {time_field}
    FROM buy_guard_table
    WHERE room_id = $1 AND {time_field} BETWEEN $2 AND $3

    UNION ALL

    SELECT 'super_chat_table' as table_type,
           0 as total_coin, price as gift_per_price, gift_num, {time_field}
    FROM super_chat_table
    WHERE room_id = $1 AND {time_field} BETWEEN $2 AND $3
"""

# ============================================================================
# LIVE SESSION QUERIES - Optimized versions
# ============================================================================

LIVE_SESSION_BY_LIVE_ID_QUERY = """
    SELECT danmu_count, start_time_str, end_time_str, parent_area, area,
           cover, title, income, watch_change_count, like_info_update_count,
           pay_count, interaction_count, max_online_rank, enter_room_count,
           ave_online_rank, ave_enter_room
    FROM live_session_table
    WHERE live_id = $1
"""

LIVE_SESSIONS_BY_ROOM_ID_QUERY = """
    SELECT live_id, danmu_count, start_time_str, end_time_str, income,
           watch_change_count, ave_online_rank, ave_enter_room,
           enter_room_count, datetime
    FROM live_session_table
    WHERE room_id = $1
    ORDER BY datetime DESC, start_time_str DESC
"""

LIVE_STATUS_ACTIONS_BY_LIVE_ID_QUERY = """
    SELECT DISTINCT live_action
    FROM live_status_minute_table
    WHERE live_id = $1
"""

LIVE_STATUS_INFO_BY_LIVE_ID_QUERY = """
    SELECT live_action, parent_area, area, cover, title
    FROM live_status_minute_table
    WHERE live_id = $1
    ORDER BY timestamp DESC
    LIMIT 1
"""

# ============================================================================
# MINUTE DATA AGGREGATION QUERIES - Optimized for analytics
# ============================================================================

MINUTE_DATA_AGGREGATION_QUERY = """
    SELECT COALESCE({aggregation}({field}), 0) as value
    FROM {table_name}
    WHERE live_id = $1 AND timestamp >= $2 AND timestamp < $3
"""

# ============================================================================
# TIME RANGE QUERIES - Optimized for live start/end time detection
# ============================================================================

LIVE_START_END_TIME_BY_DATE_QUERY = """
    SELECT timestamp, live_status, live_action
    FROM live_status_minute_table
    WHERE room_id = $1
      AND datetime >= $2::DATE
      AND datetime < $2::DATE + INTERVAL '1 DAY'
      AND (live_action = '开始直播' OR live_action = '结束直播')
    ORDER BY datetime
"""

LIVE_START_TIME_BY_END_TIME_QUERY = """
    SELECT timestamp
    FROM live_status_minute_table
    WHERE room_id = $1
      AND datetime >= $2 - INTERVAL '24 HOURS'
      AND datetime < $2
      AND live_action = '开始直播'
    ORDER BY datetime DESC
    LIMIT 1
"""
