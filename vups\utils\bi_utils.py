# /*
#  * @Author: yuymf
#  * @Date: 2025-08-25 15:52:53
#  * @Last Modified by:   yuymf
#  * @Last Modified time: 2025-08-25 15:52:53
#  */
from datetime import datetime, timedelta
from functools import lru_cache
import math
from typing import Dict, Any, List
from vups import logger
from vups.config import read_vups_config, VUPS_PROJECT_ROOT

# ---------------------------- STRING UTILS ----------------------------


@lru_cache(maxsize=None)
def get_user_mid(char):
    """
    char maybe fullname / uid / shortNmae / enName
    """
    vups = read_vups_config()
    for vup in vups:
        if str(vup["uid"]) == char:
            return char
    uid = get_vup_uid_by_any_name(char)
    if uid is not None:
        return str(uid)

    char_zh = get_zh_role_name(char)
    uid = get_vup_uid_by_short_name(char_zh)
    if uid is None:
        raise Exception(f"Character {str(char)} not found in vups.json") #TODO
    return str(uid)


@lru_cache(maxsize=None)
def get_user_fullname(uid: str):
    vups = read_vups_config()
    for vup in vups:
        if str(vup["uid"]) == uid:
            return vup["fullName"]
    return None


@lru_cache(maxsize=None)
def get_user_room_id(char):
    """
    char maybe fullname / uid / shortNmae / enName
    """
    vups = read_vups_config()
    for vup in vups:
        if str(vup["uid"]) == char:
            return vup["roomId"]
    char_zh = get_zh_role_name(char)
    room_id = get_vup_room_id_by_short_name(char_zh)
    if room_id is None:
        logger.warning(f"Character {str(char)} not found in vups.json")
        return None
    return str(room_id)

def get_zh_role_name(role_name, vup_dict: dict = None):

    vups = read_vups_config(vup_dict)

    # First check shortName
    for vup in vups:
        if vup["shortName"] == role_name:
            return role_name

    # Then check enName
    for vup in vups:
        if vup["enName"] == role_name:
            return vup["shortName"]

    # Finally check fullName
    for vup in vups:
        if vup["fullName"] == role_name:
            return vup["shortName"]

    return NotImplementedError

def get_vup_uid_by_any_name(name, vup_dict: dict = None):
    """
    Get VUP UID by any name (fullName, shortName, or enName)

    Args:
        name (str): Any name of the VUP (fullName, shortName, or enName)

    Returns:
        int: VUP UID or None if not found
    """
    vups = read_vups_config(vup_dict)

    # Check shortName first
    for vup in vups:
        if vup["shortName"] == name:
            return vup["uid"]

    # Then check enName
    for vup in vups:
        if vup["enName"] == name:
            return vup["uid"]

    # Finally check fullName
    for vup in vups:
        if vup["fullName"] == name:
            return vup["uid"]

    return None

def get_vup_uid_by_short_name(short_name):
    """
    Get VUP UID by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        int: VUP UID or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["uid"] if vup else None

def get_vup_by_short_name(short_name):
    """
    Get VUP configuration by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        dict: VUP configuration dictionary or None if not found
    """
    vups = read_vups_config()
    for vup in vups:
        if vup["shortName"] == short_name:
            return vup
    return None

def get_mid_with_role_name(role_name):

    # First try to get UID directly by any name (fullName, shortName, enName)
    uid = get_vup_uid_by_any_name(role_name)
    if uid is not None:
        return str(uid)

    # Fallback to original logic for backward compatibility
    uid = get_vup_uid_by_short_name(role_name)
    if uid is None:
        raise Exception(f"Character {role_name} not found in vups.json")
    return str(uid)

def get_vup_tieba_name_by_short_name(short_name):
    """
    Get VUP tieba name by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        str: VUP tieba name or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["tiebaName"] if vup else None

def get_vtuber_list():
    """
    Get list of all VTuber short names from vups.json

    Returns:
        list: List of VTuber short names
    """
    vups = read_vups_config()
    return [vup["shortName"] for vup in vups]

def get_vup_room_id_by_short_name(short_name):
    """
    Get VUP room ID by short name

    Args:
        short_name (str): Short name of the VUP

    Returns:
        int: VUP room ID or None if not found
    """
    vup = get_vup_by_short_name(short_name)
    return vup["roomId"] if vup else None
# ----------------------------------------------------------------------

# ----------------------------  BILIBILI  UTILS ----------------------------
XOR_CODE = 23442827791579
MASK_CODE = 2251799813685247
MAX_AID = 1 << 51
ALPHABET = "FcwAPNKTMug3GV5Lj7EJnHpWsx4tb8haYeviqBz6rkCy12mUSDQX9RdoZf"
ENCODE_MAP = 8, 7, 0, 5, 1, 3, 2, 4, 6
DECODE_MAP = tuple(reversed(ENCODE_MAP))

BASE = len(ALPHABET)
PREFIX = "BV1"
PREFIX_LEN = len(PREFIX)
CODE_LEN = len(ENCODE_MAP)


def av2bv(aid: int) -> str:
    bvid = [""] * 9
    tmp = (MAX_AID | aid) ^ XOR_CODE
    for i in range(CODE_LEN):
        bvid[ENCODE_MAP[i]] = ALPHABET[tmp % BASE]
        tmp //= BASE
    return PREFIX + "".join(bvid)


def bv2av(bvid: str):
    assert bvid[:3] == PREFIX

    bvid = bvid[3:]
    tmp = 0
    for i in range(CODE_LEN):
        idx = ALPHABET.index(bvid[DECODE_MAP[i]])
        tmp = tmp * BASE + idx
    return (tmp & MASK_CODE) ^ XOR_CODE

# ----------------------------------------------------------------------

# ----------------------------  DATE  UTILS ----------------------------

def get_date_range(recent_days: int):
    end_date = datetime.now()
    start_date = end_date - timedelta(days=recent_days)

    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")

    return start_date_str, end_date_str

def get_timestamp():
    now = datetime.now()
    formatted_timestamp = now.strftime("[%Y-%m-%d %H:%M:%S.%f]")
    return formatted_timestamp

def get_nested_value(d: Dict[str, Any], keys: List[str], default: Any = None) -> Any:
    """
    Get nested value from dictionary using key path

    Args:
        d: Dictionary to search in
        keys: List of keys representing the path
        default: Default value if path not found

    Returns:
        Value at the specified path or default
    """
    for key in keys:
        if isinstance(d, dict):
            d = d.get(key)
        else:
            return default
        if d is None:
            return default
    return d

# -----------------------------------------------------------------------

# ----------------------------  CALC  UTILS ----------------------------

def safe_int(value):
    if value is None:
        return 0
    try:
        return int(value)
    except (ValueError, TypeError):
        return 0

def video_calculate_hotness(views, comments, honors, likes, coins, favorites):
    max_views = 5000000
    max_comments = 20000
    max_honors = 4
    max_likes = 100000
    max_coins = 50000
    max_favors = 20000

    hotness = (
        round(2.4 * math.log10(views + 1) / math.log10(max_views), 2)
        + round(0.8 * math.log10(comments + 1) / math.log10(max_comments), 2)
        + round(1.6 * math.log10(likes + 1) / math.log10(max_likes), 2)
        + round(1.6 * math.log10(coins + 1) / math.log10(max_coins), 2)
        + round(1.6 * math.log10(favorites + 1) / math.log10(max_favors), 2)
        + honors * 2 / max_honors
    )
    return min(10, hotness)


def dynamic_calculate_hotness(sends, comments, likes):
    max_sends = 5000
    max_comments = 25000
    max_likes = 40000

    hotness = (
        3 * math.log10(min(sends + 1, max_sends)) / math.log10(max_sends)
        + 4 * math.log10(min(comments + 1, max_comments)) / math.log10(max_comments)
        + 3 * math.log10(min(likes + 1, max_likes)) / math.log10(max_likes)
    )
    return min(10, hotness)

# ----------------------------  WORDCLOUD  UTILS ------------------------
import jieba
from wordcloud import WordCloud
import numpy as np
from PIL import Image

# TODO: UPGRADE
STOPWORDS_ZH = set(
    map(
        str.strip,
        open(
            f"{VUPS_PROJECT_ROOT}/assets/txts/stop_words_zh.txt", encoding="utf-8"
        ).readlines(),
    )
)


def word_cloud_gen(context_list: list, target_path: str, vtuber_name="xingtong"):

    char_zh = get_zh_role_name(vtuber_name)
    mask_image = np.array(
        Image.open(f"{VUPS_PROJECT_ROOT}/assets/images/vtubers/{char_zh}.jpg") # TODO: Setup Get
    )

    context = " ".join(context_list)
    words = jieba.lcut(context)
    newtxt = " ".join(words)

    wordcloud = WordCloud(
        width=800,
        height=800,
        background_color="white",
        mask=mask_image,
        stopwords=STOPWORDS_ZH,
        max_words=500,
        min_font_size=2,
        contour_width=0.5,
        font_path=f"{VUPS_PROJECT_ROOT}/assets/fonts/msyh.ttf",
        contour_color="yellow",
    ).generate(newtxt)

    wordcloud.to_file(target_path)

# -----------------------------------------------------------------------
