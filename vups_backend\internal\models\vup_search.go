package models

// VUPSearchRequest represents a VUP search request
type VUPSearchRequest struct {
	Question string `json:"question" binding:"required" example:"查询星瞳的最新数据"`
	Stream   bool   `json:"stream" example:"false"`
}

// VUPSearchResponse represents a VUP search response
type VUPSearchResponse struct {
	Answer string `json:"answer" example:"根据最新数据，星瞳的粉丝数为..."`
	TaskID string `json:"task_id,omitempty" example:"task_123456"`
}

// VUPSearchStreamEvent represents a streaming search event
type VUPSearchStreamEvent struct {
	Type    string `json:"type" example:"data"`
	Content string `json:"content" example:"正在查询数据..."`
	Done    bool   `json:"done" example:"false"`
}
