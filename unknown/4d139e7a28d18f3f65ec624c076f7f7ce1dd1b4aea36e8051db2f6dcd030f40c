package redis

import "errors"

var (
	// ErrKeyNotFound is returned when a key is not found in Redis
	ErrKeyNotFound = errors.New("key not found")
	
	// ErrConnectionFailed is returned when Redis connection fails
	ErrConnectionFailed = errors.New("redis connection failed")
	
	// ErrMarshalFailed is returned when JSON marshaling fails
	ErrMarshalFailed = errors.New("failed to marshal data")
	
	// ErrUnmarshalFailed is returned when JSON unmarshaling fails
	ErrUnmarshalFailed = errors.New("failed to unmarshal data")
)
