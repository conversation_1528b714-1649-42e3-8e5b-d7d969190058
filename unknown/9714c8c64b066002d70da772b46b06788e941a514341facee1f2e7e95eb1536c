package models

import "time"

// LiveInfoRequest represents a live info query request
type LiveInfoRequest struct {
	RoomID    string `form:"room_id" binding:"required" example:"22886883"`
	StartTime string `form:"start_time" example:"2024-01-01"`
	EndTime   string `form:"end_time" example:"2024-01-31"`
	DataType  string `form:"data_type" example:"danmu"`
}

// DanmuData represents danmu data structure
type DanmuData struct {
	ID       int       `json:"id"`
	RoomID   string    `json:"room_id"`
	UserID   string    `json:"user_id"`
	UserName string    `json:"user_name"`
	Message  string    `json:"message"`
	DateTime time.Time `json:"datetime"`
}

// SuperChatData represents superchat data structure
type SuperChatData struct {
	ID       int       `json:"id"`
	RoomID   string    `json:"room_id"`
	UserID   string    `json:"user_id"`
	UserName string    `json:"user_name"`
	Price    float64   `json:"price"`
	Message  string    `json:"message"`
	DateTime time.Time `json:"datetime"`
}

// LiveStatusData represents live status data structure
type LiveStatusData struct {
	ID         int       `json:"id"`
	RoomID     string    `json:"room_id"`
	LiveID     string    `json:"live_id"`
	Status     int       `json:"status"`
	Title      string    `json:"title"`
	Cover      string    `json:"cover"`
	ParentArea string    `json:"parent_area"`
	Area       string    `json:"area"`
	DateTime   time.Time `json:"datetime"`
}

// LiveAnalyticsData represents live analytics data structure
type LiveAnalyticsData struct {
	LiveID              string    `json:"live_id"`
	DanmuCount          int       `json:"danmu_count"`
	StartTime           time.Time `json:"start_time"`
	EndTime             time.Time `json:"end_time"`
	Income              float64   `json:"income"`
	WatchChangeCount    int       `json:"watch_change_count"`
	PayCount            int       `json:"pay_count"`
	InteractionCount    int       `json:"interaction_count"`
	MaxOnlineRank       int       `json:"max_online_rank"`
	EnterRoomCount      int       `json:"enter_room_count"`
	AverageOnlineRank   int       `json:"average_online_rank"`
	AverageEnterRoom    int       `json:"average_enter_room"`
}
