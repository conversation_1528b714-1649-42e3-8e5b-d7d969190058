package models

import "time"

// UserDataRequest represents a user data query request
type UserDataRequest struct {
	UID       string `uri:"uid" binding:"required" example:"401315430"`
	StartTime string `form:"start_time" example:"2024-01-01"`
	EndTime   string `form:"end_time" example:"2024-01-31"`
	DataType  string `form:"data_type" example:"stats"`
}

// UserStatsData represents user statistics data structure
type UserStatsData struct {
	UID              string    `json:"uid"`
	FollowerCount    int       `json:"follower_count"`
	DahanghaiCount   int       `json:"dahanghai_count"`
	ViewCount        int       `json:"view_count"`
	LikeCount        int       `json:"like_count"`
	VideoCount       int       `json:"video_count"`
	DynamicCount     int       `json:"dynamic_count"`
	LastUpdated      time.Time `json:"last_updated"`
}

// UserContentData represents user content data structure
type UserContentData struct {
	Videos   []VideoData   `json:"videos"`
	Dynamics []DynamicData `json:"dynamics"`
}

// VideoData represents video data structure
type VideoData struct {
	BVID        string    `json:"bvid"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Duration    int       `json:"duration"`
	ViewCount   int       `json:"view_count"`
	LikeCount   int       `json:"like_count"`
	CoinCount   int       `json:"coin_count"`
	ShareCount  int       `json:"share_count"`
	PublishTime time.Time `json:"publish_time"`
}

// DynamicData represents dynamic data structure
type DynamicData struct {
	DynamicID   string    `json:"dynamic_id"`
	Content     string    `json:"content"`
	Type        int       `json:"type"`
	ViewCount   int       `json:"view_count"`
	LikeCount   int       `json:"like_count"`
	ShareCount  int       `json:"share_count"`
	PublishTime time.Time `json:"publish_time"`
}

// UserAnalyticsData represents user analytics data structure
type UserAnalyticsData struct {
	UID                string              `json:"uid"`
	FollowerGrowthRate float64             `json:"follower_growth_rate"`
	DahanghaiGrowthRate float64            `json:"dahanghai_growth_rate"`
	TopComments        []CommentData       `json:"top_comments"`
	TopVideos          []VideoData         `json:"top_videos"`
	RecentActivity     []ActivityData      `json:"recent_activity"`
}

// CommentData represents comment data structure
type CommentData struct {
	CommentID   string    `json:"comment_id"`
	Content     string    `json:"content"`
	LikeCount   int       `json:"like_count"`
	ReplyCount  int       `json:"reply_count"`
	UserName    string    `json:"user_name"`
	PublishTime time.Time `json:"publish_time"`
}

// ActivityData represents activity data structure
type ActivityData struct {
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Value       float64   `json:"value"`
	Timestamp   time.Time `json:"timestamp"`
}

// UserInfoData represents comprehensive user information
type UserInfoData struct {
	UID         string    `json:"uid"`
	Name        string    `json:"name"`
	Avatar      string    `json:"avatar"`
	Description string    `json:"description"`
	Level       int       `json:"level"`
	VIPStatus   int       `json:"vip_status"`
	JoinTime    time.Time `json:"join_time"`
	Stats       UserStatsData `json:"stats"`
}
