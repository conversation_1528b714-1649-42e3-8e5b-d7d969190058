"""
<PERSON><PERSON><PERSON> to create optimized indexes for user comment tables.
This script dynamically creates indexes for video_comment_{uid} and dynamics_comment_{uid} tables.
"""

import asyncio
from typing import List, Set

import asyncpg
from vups.logger import logger
from vups_server.sql.db_pool import get_connection


class CommentIndexManager:
    """Manager for creating and maintaining comment table indexes."""

    def __init__(self):
        self.video_comment_indexes = [
            "idx_video_comment_{uid}_time",
            "idx_video_comment_{uid}_likes",
            "idx_video_comment_{uid}_bvid",
            "idx_video_comment_{uid}_user",
            "idx_video_comment_{uid}_content"
        ]

        self.dynamics_comment_indexes = [
            "idx_dynamics_comment_{uid}_time",
            "idx_dynamics_comment_{uid}_likes",
            "idx_dynamics_comment_{uid}_dynamic",
            "idx_dynamics_comment_{uid}_user",
            "idx_dynamics_comment_{uid}_content"
        ]

    async def get_existing_comment_tables(self) -> Set[str]:
        """
        Get list of existing comment tables.

        Returns:
            Set of comment table names
        """
        query = """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND (table_name LIKE 'video_comment_%' OR table_name LIKE 'dynamics_comment_%')
        """

        try:
            async with get_connection() as conn:
                results = await conn.fetch(query)
                return {row["table_name"] for row in results}
        except Exception as e:
            logger.error(f"Failed to get comment tables: {e}")
            return set()

    async def extract_uid_from_table_name(self, table_name: str) -> str:
        """
        Extract UID from comment table name.

        Args:
            table_name: Name of the comment table

        Returns:
            UID string
        """
        if table_name.startswith("video_comment_"):
            return table_name[14:]  # Remove "video_comment_" prefix
        elif table_name.startswith("dynamics_comment_"):
            return table_name[17:]  # Remove "dynamics_comment_" prefix
        return ""

    async def create_video_comment_indexes(self, uid: str) -> bool:
        """
        Create indexes for a video comment table.

        Args:
            uid: User UID

        Returns:
            True if successful, False otherwise
        """
        table_name = f"video_comment_{uid}"

        index_queries = [
            f"""
            CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_time
            ON {table_name} (comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_likes
            ON {table_name} (like_num DESC, comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_bvid
            ON {table_name} (bvid, comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_user
            ON {table_name} (comment_user_uid, comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_video_comment_{uid}_content
            ON {table_name} (comment_time DESC)
            WHERE comment_content IS NOT NULL AND LENGTH(comment_content) > 5
            """
        ]

        try:
            async with get_connection() as conn:
                for query in index_queries:
                    await conn.execute(query)
                logger.info(f"Created indexes for video comment table: {table_name}")
                return True
        except Exception as e:
            logger.error(f"Failed to create indexes for {table_name}: {e}")
            return False

    async def create_dynamics_comment_indexes(self, uid: str) -> bool:
        """
        Create indexes for a dynamics comment table.

        Args:
            uid: User UID

        Returns:
            True if successful, False otherwise
        """
        table_name = f"dynamics_comment_{uid}"

        index_queries = [
            f"""
            CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_time
            ON {table_name} (comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_likes
            ON {table_name} (like_num DESC, comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_dynamic
            ON {table_name} (dynamic_id, comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_user
            ON {table_name} (comment_user_uid, comment_time DESC)
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_dynamics_comment_{uid}_content
            ON {table_name} (comment_time DESC)
            WHERE comment_content IS NOT NULL AND LENGTH(comment_content) > 5
            """
        ]

        try:
            async with get_connection() as conn:
                for query in index_queries:
                    await conn.execute(query)
                logger.info(f"Created indexes for dynamics comment table: {table_name}")
                return True
        except Exception as e:
            logger.error(f"Failed to create indexes for {table_name}: {e}")
            return False

    async def create_all_comment_indexes(self) -> dict:
        """
        Create indexes for all existing comment tables.

        Returns:
            Dictionary with results for each table
        """
        comment_tables = await self.get_existing_comment_tables()
        results = {
            "video_tables": {},
            "dynamics_tables": {},
            "total_processed": 0,
            "successful": 0,
            "failed": 0
        }

        for table_name in comment_tables:
            uid = await self.extract_uid_from_table_name(table_name)
            if not uid:
                continue

            results["total_processed"] += 1

            if table_name.startswith("video_comment_"):
                success = await self.create_video_comment_indexes(uid)
                results["video_tables"][uid] = success
            elif table_name.startswith("dynamics_comment_"):
                success = await self.create_dynamics_comment_indexes(uid)
                results["dynamics_tables"][uid] = success
            else:
                continue

            if success:
                results["successful"] += 1
            else:
                results["failed"] += 1

        logger.info(
            f"Comment index creation completed: "
            f"{results['successful']}/{results['total_processed']} successful"
        )

        return results

    async def check_index_usage(self, table_name: str) -> List[dict]:
        """
        Check index usage statistics for a table.

        Args:
            table_name: Name of the table to check

        Returns:
            List of index usage statistics
        """
        query = """
            SELECT indexname, idx_scan, idx_tup_read, idx_tup_fetch
            FROM pg_stat_user_indexes
            WHERE tablename = $1
            ORDER BY idx_scan DESC
        """

        try:
            async with get_connection() as conn:
                results = await conn.fetch(query, table_name)
                return [
                    {
                        "index_name": row["indexname"],
                        "scans": row["idx_scan"],
                        "tuples_read": row["idx_tup_read"],
                        "tuples_fetched": row["idx_tup_fetch"]
                    }
                    for row in results
                ]
        except Exception as e:
            logger.error(f"Failed to check index usage for {table_name}: {e}")
            return []

    async def analyze_comment_tables(self) -> dict:
        """
        Analyze all comment tables and update statistics.

        Returns:
            Dictionary with analysis results
        """
        comment_tables = await self.get_existing_comment_tables()
        results = {
            "analyzed_tables": [],
            "total_tables": len(comment_tables),
            "successful": 0,
            "failed": 0
        }

        for table_name in comment_tables:
            try:
                async with get_connection() as conn:
                    await conn.execute(f"ANALYZE {table_name}")
                results["analyzed_tables"].append(table_name)
                results["successful"] += 1
                logger.debug(f"Analyzed table: {table_name}")
            except Exception as e:
                logger.error(f"Failed to analyze table {table_name}: {e}")
                results["failed"] += 1

        logger.info(
            f"Table analysis completed: "
            f"{results['successful']}/{results['total_tables']} successful"
        )

        return results


async def main():
    """Main function to create comment table indexes."""
    manager = CommentIndexManager()

    logger.info("Starting comment table index creation...")
    results = await manager.create_all_comment_indexes()

    logger.info("Starting comment table analysis...")
    analysis_results = await manager.analyze_comment_tables()

    print(f"Index Creation Results:")
    print(f"  Total tables processed: {results['total_processed']}")
    print(f"  Successful: {results['successful']}")
    print(f"  Failed: {results['failed']}")
    print(f"  Video comment tables: {len(results['video_tables'])}")
    print(f"  Dynamics comment tables: {len(results['dynamics_tables'])}")

    print(f"\nTable Analysis Results:")
    print(f"  Total tables analyzed: {analysis_results['total_tables']}")
    print(f"  Successful: {analysis_results['successful']}")
    print(f"  Failed: {analysis_results['failed']}")


if __name__ == "__main__":
    asyncio.run(main())
