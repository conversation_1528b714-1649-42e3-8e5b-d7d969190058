"""
Task Decorators for VUPs Server

Provides decorators for controlling data pulling functions including:
- Pull frequency control
- Pull timing/scheduling
- Round-robin task registration
- Cookie management
- Error handling and retries
"""

import asyncio
import functools
import time
from datetime import datetime, timed<PERSON>ta
from typing import Callable, Optional, Dict, Any, List, Union
from enum import Enum

from vups.logger import logger
from vups_server.base.round_robin_scheduler import TaskDefinition, TaskPriority


class ScheduleMode(Enum):
    """Schedule execution modes"""
    FREQUENCY = "frequency"  # Execute every N minutes
    CRON = "cron"           # Execute based on cron schedule
    MANUAL = "manual"       # Execute only when called manually


class TaskRegistry:
    """Registry for decorated tasks"""

    def __init__(self):
        self.tasks: Dict[str, TaskDefinition] = {}
        self.function_metadata: Dict[str, Dict[str, Any]] = {}

    def register_task(self, task_def: TaskDefinition, func: Callable):
        """Register a task definition with its function"""
        self.tasks[task_def.name] = task_def
        self.function_metadata[task_def.name] = {
            'function': func,
            'last_execution': None,
            'execution_count': 0,
            'total_execution_time': 0,
            'error_count': 0
        }
        logger.debug(f"Registered task: {task_def.name}")

    def get_task(self, task_name: str) -> Optional[TaskDefinition]:
        """Get task definition by name"""
        return self.tasks.get(task_name)

    def get_all_tasks(self) -> Dict[str, TaskDefinition]:
        """Get all registered tasks"""
        return self.tasks.copy()

    def update_execution_stats(self, task_name: str, execution_time: float, success: bool):
        """Update execution statistics for a task"""
        if task_name in self.function_metadata:
            metadata = self.function_metadata[task_name]
            metadata['last_execution'] = datetime.now()
            metadata['execution_count'] += 1
            metadata['total_execution_time'] += execution_time
            if not success:
                metadata['error_count'] += 1


# Global task registry
_task_registry = TaskRegistry()


def get_task_registry() -> TaskRegistry:
    """Get the global task registry"""
    return _task_registry


def pull_frequency(minutes: int,
                  priority: TaskPriority = TaskPriority.NORMAL,
                  max_execution_time: int = 300,
                  retry_count: int = 3,
                  retry_delay: int = 60):
    """
    Decorator to control pull frequency for data pulling functions

    Args:
        minutes: Execute every N minutes
        priority: Task priority level
        max_execution_time: Maximum execution time in seconds
        retry_count: Number of retries on failure
        retry_delay: Delay between retries in seconds
    """
    def decorator(func: Callable) -> Callable:
        # Extract server type from function's class
        server_type = getattr(func, '__self__', None)
        if hasattr(server_type, 'task_type'):
            task_server_type = server_type.task_type
        else:
            # Fallback: try to infer from function name or class name
            task_server_type = _infer_server_type(func)

        task_def = TaskDefinition(
            name=f"{task_server_type}_{func.__name__}",
            server_type=task_server_type,
            function_name=func.__name__,
            priority=priority,
            frequency_minutes=minutes,
            max_execution_time=max_execution_time,
            retry_count=retry_count,
            retry_delay=retry_delay,
            requires_cookie=True
        )

        _task_registry.register_task(task_def, func)

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True

            try:
                logger.debug(f"Executing {func.__name__} with frequency control ({minutes} min)")
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Error in {func.__name__}: {e}")
                raise
            finally:
                execution_time = time.time() - start_time
                _task_registry.update_execution_stats(task_def.name, execution_time, success)

        # Add metadata to function
        wrapper._task_definition = task_def
        wrapper._schedule_mode = ScheduleMode.FREQUENCY

        return wrapper
    return decorator


def pull_schedule(hour: Optional[int] = None,
                 minute: Optional[int] = None,
                 day_of_week: Optional[str] = None,
                 cron_expression: Optional[str] = None,
                 priority: TaskPriority = TaskPriority.NORMAL,
                 max_execution_time: int = 300):
    """
    Decorator to control pull timing/scheduling for data pulling functions

    Args:
        hour: Hour to execute (0-23)
        minute: Minute to execute (0-59)
        day_of_week: Day of week (mon, tue, wed, thu, fri, sat, sun)
        cron_expression: Custom cron expression
        priority: Task priority level
        max_execution_time: Maximum execution time in seconds
    """
    def decorator(func: Callable) -> Callable:
        # Build cron expression
        if cron_expression:
            cron = cron_expression
        else:
            cron_parts = ['*'] * 5  # minute, hour, day, month, day_of_week
            if minute is not None:
                cron_parts[0] = str(minute)
            if hour is not None:
                cron_parts[1] = str(hour)
            if day_of_week:
                # Convert day names to cron format
                day_map = {
                    'mon': '1', 'tue': '2', 'wed': '3', 'thu': '4',
                    'fri': '5', 'sat': '6', 'sun': '0'
                }
                cron_parts[4] = day_map.get(day_of_week.lower(), day_of_week)
            cron = ' '.join(cron_parts)

        # Extract server type
        server_type = getattr(func, '__self__', None)
        if hasattr(server_type, 'task_type'):
            task_server_type = server_type.task_type
        else:
            task_server_type = _infer_server_type(func)

        task_def = TaskDefinition(
            name=f"{task_server_type}_{func.__name__}",
            server_type=task_server_type,
            function_name=func.__name__,
            priority=priority,
            cron_schedule=cron,
            max_execution_time=max_execution_time,
            requires_cookie=True
        )

        _task_registry.register_task(task_def, func)

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True

            try:
                logger.debug(f"Executing {func.__name__} with schedule control ({cron})")
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Error in {func.__name__}: {e}")
                raise
            finally:
                execution_time = time.time() - start_time
                _task_registry.update_execution_stats(task_def.name, execution_time, success)

        # Add metadata to function
        wrapper._task_definition = task_def
        wrapper._schedule_mode = ScheduleMode.CRON

        return wrapper
    return decorator


def round_robin_task(priority: TaskPriority = TaskPriority.NORMAL,
                    dependencies: Optional[List[str]] = None,
                    metadata: Optional[Dict[str, Any]] = None):
    """
    Decorator to register function as a round-robin task

    Args:
        priority: Task priority level
        dependencies: List of task names this task depends on
        metadata: Additional metadata for task execution
    """
    def decorator(func: Callable) -> Callable:
        # Extract server type
        server_type = getattr(func, '__self__', None)
        if hasattr(server_type, 'task_type'):
            task_server_type = server_type.task_type
        else:
            task_server_type = _infer_server_type(func)

        task_def = TaskDefinition(
            name=f"{task_server_type}_{func.__name__}",
            server_type=task_server_type,
            function_name=func.__name__,
            priority=priority,
            dependencies=dependencies or [],
            metadata=metadata or {},
            requires_cookie=True
        )

        _task_registry.register_task(task_def, func)

        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True

            try:
                logger.debug(f"Executing round-robin task {func.__name__}")
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Error in round-robin task {func.__name__}: {e}")
                raise
            finally:
                execution_time = time.time() - start_time
                _task_registry.update_execution_stats(task_def.name, execution_time, success)

        # Add metadata to function
        wrapper._task_definition = task_def
        wrapper._schedule_mode = ScheduleMode.MANUAL

        return wrapper
    return decorator


def use_task_cookie(task_type: Optional[str] = None,
                   auto_refresh: bool = True,
                   refresh_threshold_hours: int = 12):
    """
    Decorator to ensure function uses appropriate task cookie

    Args:
        task_type: Specific task type to use ('user', 'creator', 'live')
        auto_refresh: Whether to auto-refresh cookies
        refresh_threshold_hours: Hours before attempting refresh
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            # Determine task type
            if task_type:
                cookie_task_type = task_type
            elif hasattr(self, 'task_type'):
                cookie_task_type = self.task_type
            else:
                cookie_task_type = _infer_server_type(func)

            # Check and refresh credential if needed
            if auto_refresh and hasattr(self, 'refresh_credential'):
                try:
                    # Check if credential needs refresh
                    from vups_server.base.cookie_manager import get_cookie_manager
                    cookie_manager = get_cookie_manager()

                    if cookie_manager.is_refresh_needed(cookie_task_type):
                        logger.info(f"Refreshing credential for {cookie_task_type} before executing {func.__name__}")
                        await self.refresh_credential()
                except Exception as e:
                    logger.warning(f"Failed to refresh credential for {func.__name__}: {e}")

            # Validate credential
            if hasattr(self, 'validate_credential') and not self.validate_credential():
                logger.error(f"Invalid credential for {func.__name__}, execution may fail")

            return await func(self, *args, **kwargs)

        return wrapper
    return decorator


def retry_on_failure(max_retries: int = 3,
                    delay: int = 60,
                    exponential_backoff: bool = True,
                    retry_exceptions: tuple = (Exception,)):
    """
    Decorator to add retry logic to data pulling functions

    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        exponential_backoff: Whether to use exponential backoff
        retry_exceptions: Tuple of exceptions to retry on
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except retry_exceptions as e:
                    last_exception = e

                    if attempt < max_retries:
                        retry_delay = delay
                        if exponential_backoff:
                            retry_delay = delay * (2 ** attempt)

                        logger.warning(
                            f"Attempt {attempt + 1}/{max_retries + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {retry_delay} seconds..."
                        )
                        await asyncio.sleep(retry_delay)
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
                        raise last_exception

            # This should never be reached, but just in case
            if last_exception:
                raise last_exception

        return wrapper
    return decorator


def _infer_server_type(func: Callable) -> str:
    """
    Infer server type from function name or class name

    Args:
        func: Function to analyze

    Returns:
        Inferred server type ('user', 'creator', 'live')
    """
    func_name = func.__name__.lower()

    # Check function name patterns
    if 'creator' in func_name or 'overview' in func_name or 'fan' in func_name:
        return 'creator'
    elif 'live' in func_name or 'danmu' in func_name or 'gift' in func_name:
        return 'live'
    elif 'user' in func_name or 'follower' in func_name or 'dynamic' in func_name:
        return 'user'

    # Check class name if available
    if hasattr(func, '__self__'):
        class_name = func.__self__.__class__.__name__.lower()
        if 'creator' in class_name:
            return 'creator'
        elif 'live' in class_name:
            return 'live'
        elif 'user' in class_name:
            return 'user'

    # Default fallback
    logger.warning(f"Could not infer server type for {func.__name__}, defaulting to 'user'")
    return 'user'


def get_task_statistics() -> Dict[str, Any]:
    """Get statistics for all registered tasks"""
    stats = {}

    for task_name, metadata in _task_registry.function_metadata.items():
        avg_execution_time = 0
        if metadata['execution_count'] > 0:
            avg_execution_time = metadata['total_execution_time'] / metadata['execution_count']

        stats[task_name] = {
            'execution_count': metadata['execution_count'],
            'error_count': metadata['error_count'],
            'success_rate': (metadata['execution_count'] - metadata['error_count']) / max(metadata['execution_count'], 1),
            'average_execution_time': avg_execution_time,
            'last_execution': metadata['last_execution'].isoformat() if metadata['last_execution'] else None
        }

    return stats
