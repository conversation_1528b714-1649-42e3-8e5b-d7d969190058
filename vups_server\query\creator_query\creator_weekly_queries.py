"""
Creator weekly query module.
Handles weekly metrics data from weekly_metrics_table and related views.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Union, Dict, Any

import asyncpg
from vups.logger import logger
from vups_server.base.query_base import BaseQueryService


class CreatorWeeklyService(BaseQueryService):
    """Service for querying creator weekly metrics and analytics data."""

    def __init__(self):
        super().__init__(cache_ttl=300)  # 5 minutes cache for historical data

    def _date_to_timestamp(self, date_input: Union[str, datetime]) -> int:
        """Convert date to Unix timestamp (bigint format used in database)."""
        if isinstance(date_input, str):
            try:
                dt = datetime.strptime(date_input, '%Y-%m-%d')
            except ValueError:
                logger.error(f"Invalid date format: {date_input}. Expected YYYY-MM-DD")
                return 0
        elif isinstance(date_input, datetime):
            dt = date_input
        else:
            logger.error(f"Invalid date type: {type(date_input)}")
            return 0
        
        return int(dt.timestamp())

    async def get_weekly_metrics_by_uid_and_date(
        self,
        uid: str,
        date: Union[str, datetime],
        report_type: str = "overview"
    ) -> List[asyncpg.Record]:
        """
        Get weekly metrics data for a specific user, date, and report type.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object
            report_type: Type of report ('overview', 'play_analyze', 'attention_analyze')

        Returns:
            List of weekly metrics records
        """
        date_value = self._date_to_timestamp(date)
        if not date_value:
            return []

        cache_key = f"weekly_metrics_{uid}_{date_value}_{report_type}"

        query = """
            SELECT uid, report_type, metric_category, amount, amount_pass_per,
                   amount_last, amount_last_pass_per, amount_change, amount_med,
                   date_value, tendency_list, additional_data, create_time, update_time
            FROM weekly_metrics_table
            WHERE uid = $1 AND date_value = $2 AND report_type = $3
            ORDER BY metric_category
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, date_value, report_type],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} weekly metrics for UID={uid}, date={date}, type={report_type}")
        return results or []

    async def get_weekly_overview_data(
        self,
        uid: str,
        date: Union[str, datetime]
    ) -> Optional[asyncpg.Record]:
        """
        Get weekly overview data using the view for backward compatibility.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object

        Returns:
            Weekly overview record or None if not found
        """
        date_value = self._date_to_timestamp(date)
        if not date_value:
            return None

        cache_key = f"weekly_overview_{uid}_{date_value}"

        query = """
            SELECT *
            FROM weekly_key_data_overview_table
            WHERE uid = $1 AND play_cnt_date = $2
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, date_value],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved weekly overview for UID={uid}, date={date}")
        else:
            logger.info(f"No weekly overview found for UID={uid}, date={date}")

        return result

    async def get_weekly_play_analyze_data(
        self,
        uid: str,
        date: Union[str, datetime]
    ) -> Optional[asyncpg.Record]:
        """
        Get weekly play analyze data using the view.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object

        Returns:
            Weekly play analyze record or None if not found
        """
        date_value = self._date_to_timestamp(date)
        if not date_value:
            return None

        cache_key = f"weekly_play_analyze_{uid}_{date_value}"

        query = """
            SELECT *
            FROM weekly_play_analyze_table
            WHERE uid = $1 AND all_play_date = $2
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, date_value],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved weekly play analyze for UID={uid}, date={date}")
        else:
            logger.info(f"No weekly play analyze found for UID={uid}, date={date}")

        return result

    async def get_weekly_attention_analyze_data(
        self,
        uid: str,
        date: Union[str, datetime]
    ) -> Optional[asyncpg.Record]:
        """
        Get weekly attention analyze data using the view.

        Args:
            uid: User UID
            date: Date as string (YYYY-MM-DD) or datetime object

        Returns:
            Weekly attention analyze record or None if not found
        """
        date_value = self._date_to_timestamp(date)
        if not date_value:
            return None

        cache_key = f"weekly_attention_analyze_{uid}_{date_value}"

        query = """
            SELECT *
            FROM weekly_attention_analyze_table
            WHERE uid = $1 AND net_attention_cnt_date = $2
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, date_value],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved weekly attention analyze for UID={uid}, date={date}")
        else:
            logger.info(f"No weekly attention analyze found for UID={uid}, date={date}")

        return result

    async def get_past_week_data(
        self,
        uid: str,
        weeks_ago: int = 1,
        report_type: str = "overview"
    ) -> List[asyncpg.Record]:
        """
        Get weekly data for a specific number of weeks ago.

        Args:
            uid: User UID
            weeks_ago: Number of weeks ago (default: 1 for last week)
            report_type: Type of report to retrieve

        Returns:
            List of weekly metrics records
        """
        target_date = datetime.now() - timedelta(weeks=weeks_ago)
        return await self.get_weekly_metrics_by_uid_and_date(uid, target_date, report_type)

    async def get_weekly_data_by_date_range(
        self,
        uid: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        report_type: str = "overview"
    ) -> List[asyncpg.Record]:
        """
        Get weekly metrics data for a date range.

        Args:
            uid: User UID
            start_date: Start date (inclusive)
            end_date: End date (inclusive)
            report_type: Type of report to retrieve

        Returns:
            List of weekly metrics records
        """
        start_ts = self._date_to_timestamp(start_date)
        end_ts = self._date_to_timestamp(end_date)
        
        if not start_ts or not end_ts:
            return []

        cache_key = f"weekly_range_{uid}_{start_ts}_{end_ts}_{report_type}"

        query = """
            SELECT uid, report_type, metric_category, amount, amount_pass_per,
                   amount_last, amount_last_pass_per, amount_change, amount_med,
                   date_value, tendency_list, additional_data, create_time, update_time
            FROM weekly_metrics_table
            WHERE uid = $1 AND date_value >= $2 AND date_value <= $3 AND report_type = $4
            ORDER BY date_value ASC, metric_category
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, start_ts, end_ts, report_type],
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} weekly records for UID={uid}, range={start_date} to {end_date}")
        return results or []

    async def get_weekly_archive_analyze_data(
        self,
        uid: str,
        create_time: Optional[int] = None
    ) -> List[asyncpg.Record]:
        """
        Get weekly archive analyze data.

        Args:
            uid: User UID
            create_time: Optional specific create_time to filter by

        Returns:
            List of weekly archive analyze records
        """
        cache_key = f"weekly_archive_{uid}_{create_time or 'all'}"

        if create_time:
            query = """
                SELECT uid, pub_arc_list, create_time, update_time
                FROM weekly_archive_analyze_table
                WHERE uid = $1 AND create_time = $2
                ORDER BY create_time DESC
            """
            params = [uid, create_time]
        else:
            query = """
                SELECT uid, pub_arc_list, create_time, update_time
                FROM weekly_archive_analyze_table
                WHERE uid = $1
                ORDER BY create_time DESC
                LIMIT 10
            """
            params = [uid]

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=params,
            fetch_type="fetch"
        )

        logger.info(f"Retrieved {len(results)} weekly archive records for UID={uid}")
        return results or []


# Global service instance
creator_weekly_service = CreatorWeeklyService()
